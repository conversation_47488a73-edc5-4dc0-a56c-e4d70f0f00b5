import os
import copy
import json
import logging
from typing import Dict, Any, Optional
from django.conf import settings
from utils.mongo import MongoUtility
from utils.constants import (
    SubscriptionStatus,
    WebhookSource,
    SequenceType,
    UsageKeys,
    DBColls,
)
from utils import (
    generate_sequence_no,
    get_traceback,
    DateUtil
)
from schema import RazorpayCustomerSchema
from .payment_utils import (
    get_dummy_utility,
    convert_to_ms,
    Finder
)
from .razorpay import <PERSON><PERSON>pay
from .sclen import Sclen
from .exceptions import (
    PaymentProcessingError,
    PaymentValidationError,
    PaymentSecurityError,
    handle_payment_exception
)
from .security import PaymentSecurityManager
from .audit import PaymentAuditLogger, AuditEventType
from .resilience import with_resilience

logger = logging.getLogger("application")
audit_logger = PaymentAuditLogger()


class WebhookUtils(object):
    """
    Enhanced webhook processing utility with modern industry standards.

    Provides secure, auditable, and resilient webhook processing with:
    - Comprehensive security validation
    - Structured audit logging
    - Enhanced error handling
    - Idempotency protection
    - Input sanitization
    """

    _events = [
        "subscription.authenticated",
        "subscription.activated",
        "subscription.charged",
        "subscription.paused",
        "subscription.resumed",
        "subscription.cancelled",
        "subscription.pending",
        "subscription.halted",
        "subscription.completed",
        "invoice.paid",
        "invoice.expired",
        "order.paid",
        "payment.failed",
    ]

    def __init__(self, request):
        self.db = MongoUtility()
        self.razorpay = Razorpay()
        self.now = DateUtil.get_current_timestamp()
        self.request = request
        self.invoice_id = None
        self.subscription_id = None

        # Enhanced security and audit features
        self.security_manager = PaymentSecurityManager()
        self.audit_context = audit_logger.create_audit_context()

        # Initialize webhook processing
        self._initialize_webhook_processing()

        # Core webhook data
        self.request_headers = self.request.headers
        self.webhook_signature = self._get_webhook_signature()
        self.original_payload = self._parse_and_validate_payload()

        self.entity_ids = self._set_entity_ids()
        self.update_query = {}
        self.db_updates = []
        self.log_id = None

    def _initialize_webhook_processing(self):
        """Initialize webhook processing with security checks and audit logging."""
        try:
            # Log webhook initialization
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_RECEIVED,
                details={
                    'request_method': self.request.method,
                    'request_path': self.request.path,
                    'remote_addr': self.request.META.get('REMOTE_ADDR', 'unknown')
                }
            )

            # Perform initial security checks
            self._perform_initial_security_checks()

        except Exception as e:
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'error_type': 'webhook_initialization_failed',
                    'error_message': str(e)
                }
            )
            raise

    def _perform_initial_security_checks(self):
        """Perform initial security validations."""
        # Check IP whitelist if configured
        client_ip = self.request.META.get('REMOTE_ADDR', 'unknown')
        if client_ip != 'unknown':
            self.security_manager.check_ip_whitelist(client_ip)

        # Rate limiting check for webhook endpoint
        identifier = f"webhook:{client_ip}"
        self.security_manager.check_rate_limit(identifier, limit=1000, window=3600)  # Higher limit for webhooks

    def _parse_and_validate_payload(self) -> Dict[str, Any]:
        """Parse and validate webhook payload with security checks."""
        try:
            # Parse JSON payload
            raw_payload = self.request.body.decode("utf-8")
            if not raw_payload:
                raise PaymentValidationError("Empty webhook payload received")

            payload = json.loads(raw_payload)

            # Validate payload structure
            if not isinstance(payload, dict):
                raise PaymentValidationError("Invalid payload format: expected JSON object")

            # Check required fields
            required_fields = ['event', 'payload']
            for field in required_fields:
                if field not in payload:
                    raise PaymentValidationError(f"Missing required field: {field}")

            # Sanitize payload
            sanitized_payload = self.security_manager.sanitize_input(payload)

            return sanitized_payload

        except json.JSONDecodeError as e:
            raise PaymentValidationError(f"Invalid JSON payload: {str(e)}")
        except Exception as e:
            if isinstance(e, (PaymentValidationError, PaymentSecurityError)):
                raise
            raise PaymentProcessingError(f"Failed to parse webhook payload: {str(e)}")

    @handle_payment_exception
    @with_resilience(
        circuit_breaker_name="webhook_processing",
        max_retries=2,  # Limited retries for webhooks
        failure_threshold=10,
        recovery_timeout=300
    )
    def run(self) -> None:
        """
        Enhanced webhook processing with comprehensive security and error handling.

        Raises:
            PaymentValidationError: If webhook validation fails
            PaymentSecurityError: If security checks fail
            PaymentProcessingError: If webhook processing fails
        """
        try:
            # Log webhook processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_RECEIVED,
                details={
                    'event': self.original_payload.get('event'),
                    'entity': self.original_payload.get('entity')
                }
            )

            # Check for duplicate processing (idempotency)
            if self._check_if_webhook_processed():
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_PROCESSED,
                    details={'status': 'duplicate_skipped'}
                )
                return

            # Verify webhook signature
            self.verify()

            # Save original payload for audit trail
            self._save_original_payload()

            # Validate and process event
            event = self.original_payload["event"]
            if event not in WebhookUtils._events:
                raise PaymentValidationError(
                    f"Unsupported webhook event: {event}",
                    details={'supported_events': WebhookUtils._events}
                )

            # Extract entity information
            entity = self.original_payload.get("entity", "event")
            collection, collection_event = event.split(".")
            event_method = f"process_{collection}_{collection_event}_{entity}"

            if not hasattr(self, event_method):
                raise PaymentProcessingError(
                    f"Webhook handler not found: {event_method}",
                    details={
                        'event': event,
                        'entity': entity,
                        'expected_method': event_method
                    }
                )

            # Process the webhook event
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event': event,
                    'handler_method': event_method,
                    'status': 'processing'
                }
            )

            getattr(self, event_method)()

            # Log successful processing
            self._save_success_msg_in_log()

            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event': event,
                    'status': 'completed'
                }
            )

        except Exception as e:
            # Log processing failure
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'error_type': 'webhook_processing_failed',
                    'error_message': str(e),
                    'event': self.original_payload.get('event', 'unknown')
                }
            )

            # Save error to webhook log
            if hasattr(self, 'log_id') and self.log_id:
                self._save_error_msg_in_log(e)

            raise

    @handle_payment_exception
    def process_subscription_authenticated_event(self) -> None:
        """
        Process subscription authenticated event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_authenticated',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")

            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            subscription_id = rzp_subs["id"]

            # Log subscription details
            self.audit_context.log_event(
                event_type=AuditEventType.SUBSCRIPTION_CREATED,
                details={
                    'subscription_id': subscription_id,
                    'status': 'authenticated'
                }
            )

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Update customer information
            self.update_razorpay_customer(rzp_subs, set=set_flag)

            # Update local subscription status
            coll = DBColls.SUBSCRIPTIONS
            update_query = {"is_authenticated": True}
            query = {"razorpay_sub_id": subscription_id}

            # Validate that subscription exists locally
            existing_subscription = self.db.find(coll, query, find_one=True)
            if not existing_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_authenticated',
                    'subscription_id': subscription_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_AUTHENTICATED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_authenticated',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_AUTHENTICATED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_activated_event(self) -> None:
        """
        Process subscription activated event with enhanced validation and error handling.

        This method handles the activation of a subscription, updating both Razorpay
        subscription data and local subscription status with proper date conversions.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.SUBSCRIPTION_ACTIVATED,
                details={
                    'event_type': 'subscription_activated',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")

            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            subscription_id = rzp_subs.get("id")
            if not subscription_id:
                raise PaymentValidationError("Missing subscription ID in payload")

            # Validate required date fields
            required_date_fields = ["current_start", "current_end", "start_at", "end_at"]
            missing_fields = []

            for field in required_date_fields:
                if not rzp_subs.get(field):
                    missing_fields.append(field)

            if missing_fields:
                raise PaymentValidationError(
                    f"Missing required date fields: {', '.join(missing_fields)}",
                    details={'subscription_id': subscription_id, 'missing_fields': missing_fields}
                )

            # Validate date values
            try:
                current_start_ms = convert_to_ms(rzp_subs["current_start"])
                current_end_ms = convert_to_ms(rzp_subs["current_end"])
                start_date_ms = convert_to_ms(rzp_subs["start_at"])
                end_date_ms = convert_to_ms(rzp_subs["end_at"])

                # Validate date logic
                if current_start_ms >= current_end_ms:
                    raise PaymentValidationError(
                        "Invalid current cycle dates: start date must be before end date",
                        details={'current_start': current_start_ms, 'current_end': current_end_ms}
                    )

                if start_date_ms >= end_date_ms:
                    raise PaymentValidationError(
                        "Invalid subscription dates: start date must be before end date",
                        details={'start_date': start_date_ms, 'end_date': end_date_ms}
                    )

            except Exception as date_error:
                raise PaymentValidationError(
                    f"Failed to convert subscription dates: {str(date_error)}",
                    details={'subscription_id': subscription_id}
                )

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Prepare local subscription update
            coll = DBColls.SUBSCRIPTIONS
            query = {"razorpay_sub_id": subscription_id}
            update_query = {
                # Current cycle validity (monthly/yearly)
                "current_start": current_start_ms,
                "current_end": current_end_ms,
                # Subscription validity
                "start_date": start_date_ms,
                "end_date": end_date_ms,
                "status_id": SubscriptionStatus.ACTIVE.value,
                "status": SubscriptionStatus.ACTIVE.name,
            }

            # Validate that subscription exists locally
            existing_subscription = self.db.find(coll, query, find_one=True)
            if not existing_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful activation with details
            self.audit_context.log_event(
                event_type=AuditEventType.SUBSCRIPTION_ACTIVATED,
                details={
                    'subscription_id': subscription_id,
                    'current_start': current_start_ms,
                    'current_end': current_end_ms,
                    'start_date': start_date_ms,
                    'end_date': end_date_ms,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_ACTIVATED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_activated',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_ACTIVATED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_charged_event(self) -> None:
        """
        Process subscription charged event with enhanced validation and business logic.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                details={
                    'event_type': 'subscription_charged',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            # Extract and validate subscription data
            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")
            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            # Extract and validate payment data
            payment_data = actual_payload.get("payment", {})
            rzp_payment = payment_data.get("entity")
            if not rzp_payment:
                raise PaymentValidationError("Missing payment entity in payload")

            # Validate required fields
            subscription_id = rzp_subs.get("id")
            payment_id = rzp_payment.get("id")
            plan_id = rzp_subs.get("plan_id")

            if not all([subscription_id, payment_id, plan_id]):
                raise PaymentValidationError(
                    "Missing required fields in subscription charged event",
                    details={
                        'has_subscription_id': bool(subscription_id),
                        'has_payment_id': bool(payment_id),
                        'has_plan_id': bool(plan_id)
                    }
                )

            # Link payment to subscription
            rzp_payment["subscription_id"] = subscription_id

            # Validate payment amount
            payment_amount = rzp_payment.get("amount", 0)
            if payment_amount <= 0:
                raise PaymentValidationError(
                    f"Invalid payment amount: {payment_amount}",
                    details={'payment_id': payment_id}
                )

            # Log payment details
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                payment_id=payment_id,
                amount=payment_amount / 100,  # Convert from paise to rupees
                details={
                    'subscription_id': subscription_id,
                    'plan_id': plan_id
                }
            )

            # Update Razorpay entities
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            self.update_razorpay_payments(rzp_payment, plan_id=plan_id, set=set_flag)

            # Update local subscription status
            coll = DBColls.SUBSCRIPTIONS
            set_flag = not bool(self.update_query)
            sub_status = SubscriptionStatus.ACTIVE
            query = {"razorpay_sub_id": subscription_id}
            update_query = {"status": sub_status.name, "status_id": sub_status.value}

            # Validate local subscription exists
            local_subscription = self.db.find(coll, query, find_one=True)
            if not local_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Process addon payments
            notes = rzp_subs.get("notes") or {}
            sub_id = notes.get("subscription_id")

            if sub_id:
                sub_obj = self.db.find(coll, {"id": sub_id}, find_one=True)
                if sub_obj:
                    # Update addon payment status
                    addons = sub_obj.get("addons", [])
                    for i, addon in enumerate(addons):
                        addon_id = addon.get("id")
                        if addon_id and notes.get(addon_id):
                            update_query[f"addons.{i}.is_paid"] = True

                    # Reset usage lanes for the plan
                    plan = self.db.find(DBColls.PLANS, {"id": sub_obj["plan_id"]}, find_one=True)
                    if plan:
                        for feature in plan.get("features", []):
                            feature_id = feature.get("id")
                            if feature_id == UsageKeys.NUMBER_OF_LANES.value:
                                lane_count = feature.get("ival", 0)
                                update_query.update({
                                    "usage.remaining_plan_lanes": lane_count,
                                    "usage.plan_lanes": lane_count,
                                })
                                break

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.SUBSCRIPTION_RENEWED,
                details={
                    'subscription_id': subscription_id,
                    'payment_id': payment_id,
                    'amount': payment_amount / 100,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_CHARGED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_charged',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_CHARGED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_paused_event(self) -> None:
        """
        Process subscription paused event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_paused',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")

            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            subscription_id = rzp_subs.get("id")
            if not subscription_id:
                raise PaymentValidationError("Missing subscription ID in payload")

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Update local subscription status
            update_query = {
                "status": SubscriptionStatus.PAUSED.name,
                "status_id": SubscriptionStatus.PAUSED.value,
            }
            coll = DBColls.SUBSCRIPTIONS
            query = {"razorpay_sub_id": subscription_id}

            # Validate that subscription exists locally
            existing_subscription = self.db.find(coll, query, find_one=True)
            if not existing_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_paused',
                    'subscription_id': subscription_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_PAUSED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_paused',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_PAUSED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_resumed_event(self) -> None:
        """
        Process subscription resumed event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_resumed',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")

            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            subscription_id = rzp_subs.get("id")
            if not subscription_id:
                raise PaymentValidationError("Missing subscription ID in payload")

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Update local subscription status to active
            update_query = {
                "status": SubscriptionStatus.ACTIVE.name,
                "status_id": SubscriptionStatus.ACTIVE.value,
            }
            coll = DBColls.SUBSCRIPTIONS
            query = {"razorpay_sub_id": subscription_id}

            # Validate that subscription exists locally
            existing_subscription = self.db.find(coll, query, find_one=True)
            if not existing_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_resumed',
                    'subscription_id': subscription_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_RESUMED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_resumed',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_RESUMED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_cancelled_event(self) -> None:
        """
        Process subscription cancelled event with enhanced validation and business logic.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.SUBSCRIPTION_CANCELLED,
                details={
                    'event_type': 'subscription_cancelled',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")

            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            subscription_id = rzp_subs.get("id")
            if not subscription_id:
                raise PaymentValidationError("Missing subscription ID in payload")

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Check local subscription and apply business logic
            coll = DBColls.SUBSCRIPTIONS
            query = {"razorpay_sub_id": subscription_id}
            subscription = self.db.find(coll, query, find_one=True)

            if not subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Business logic: Don't cancel trial subscriptions
            if subscription["status_id"] != SubscriptionStatus.TRIAL.value:
                update_query = {
                    "status": SubscriptionStatus.CANCELLED.name,
                    "status_id": SubscriptionStatus.CANCELLED.value,
                }

                # Perform database update
                self.db.update(coll, query, update_query)
                self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

                # Log cancellation
                self.audit_context.log_event(
                    event_type=AuditEventType.SUBSCRIPTION_CANCELLED,
                    details={
                        'subscription_id': subscription_id,
                        'previous_status': subscription.get('status'),
                        'action': 'cancelled'
                    }
                )
            else:
                # Log trial subscription skip
                self.audit_context.log_event(
                    event_type=AuditEventType.SUBSCRIPTION_CANCELLED,
                    details={
                        'subscription_id': subscription_id,
                        'status': subscription.get('status'),
                        'action': 'skipped_trial'
                    }
                )

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_cancelled',
                    'subscription_id': subscription_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_CANCELLED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_cancelled',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_CANCELLED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_pending_event(self) -> None:
        """
        Process subscription pending event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_pending',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")

            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            subscription_id = rzp_subs.get("id")
            if not subscription_id:
                raise PaymentValidationError("Missing subscription ID in payload")

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Update local subscription status to pending
            update_query = {
                "status": SubscriptionStatus.PENDING.name,
                "status_id": SubscriptionStatus.PENDING.value,
            }
            coll = DBColls.SUBSCRIPTIONS
            query = {"razorpay_sub_id": subscription_id}

            # Validate that subscription exists locally
            existing_subscription = self.db.find(coll, query, find_one=True)
            if not existing_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_pending',
                    'subscription_id': subscription_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_PENDING_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_pending',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_PENDING_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_halted_event(self) -> None:
        """
        Process subscription halted event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_halted',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")

            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            subscription_id = rzp_subs.get("id")
            if not subscription_id:
                raise PaymentValidationError("Missing subscription ID in payload")

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Update local subscription status to halted
            update_query = {
                "status": SubscriptionStatus.HALTED.name,
                "status_id": SubscriptionStatus.HALTED.value,
            }
            coll = DBColls.SUBSCRIPTIONS
            query = {"razorpay_sub_id": subscription_id}

            # Validate that subscription exists locally
            existing_subscription = self.db.find(coll, query, find_one=True)
            if not existing_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_halted',
                    'subscription_id': subscription_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_HALTED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_halted',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_HALTED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_completed_event(self) -> None:
        """
        Process subscription completed event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If subscription processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_completed',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            subscription_data = actual_payload.get("subscription", {})
            rzp_subs = subscription_data.get("entity")
            if not rzp_subs:
                raise PaymentValidationError("Missing subscription entity in payload")

            payment_data = actual_payload.get("payment", {})
            rzp_payment = payment_data.get("entity")
            if not rzp_payment:
                raise PaymentValidationError("Missing payment entity in payload")

            subscription_id = rzp_subs.get("id")
            payment_id = rzp_payment.get("id")
            plan_id = rzp_subs.get("plan_id")

            if not all([subscription_id, payment_id, plan_id]):
                raise PaymentValidationError(
                    "Missing required fields in subscription completed event",
                    details={
                        'has_subscription_id': bool(subscription_id),
                        'has_payment_id': bool(payment_id),
                        'has_plan_id': bool(plan_id)
                    }
                )

            # Update Razorpay subscription data
            self.update_razorpay_subscription(rzp_subs)
            set_flag = not bool(self.update_query)

            # Update payment data
            self.update_razorpay_payments(rzp_payment, plan_id=plan_id, set=set_flag)

            # Update local subscription status to completed
            set_flag = not bool(self.update_query)
            update_query = {
                "status": SubscriptionStatus.COMPLETED.name,
                "status_id": SubscriptionStatus.COMPLETED.value,
            }
            coll = DBColls.SUBSCRIPTIONS
            query = {"razorpay_sub_id": subscription_id}

            # Validate that subscription exists locally
            existing_subscription = self.db.find(coll, query, find_one=True)
            if not existing_subscription:
                raise PaymentProcessingError(
                    f"Local subscription not found for Razorpay ID: {subscription_id}",
                    details={'razorpay_subscription_id': subscription_id}
                )

            # Perform database update
            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set_flag)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'subscription_completed',
                    'subscription_id': subscription_id,
                    'payment_id': payment_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[SUBSCRIPTION_COMPLETED_EVENT] processed successfully for subscription {subscription_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'subscription_completed',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[SUBSCRIPTION_COMPLETED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_invoice_paid_event(self) -> None:
        """
        Process invoice paid event with enhanced validation and business logic.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If invoice processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                details={
                    'event_type': 'invoice_paid',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            # Extract and validate invoice data
            invoice_data = actual_payload.get("invoice", {})
            invoice = invoice_data.get("entity")
            if not invoice:
                raise PaymentValidationError("Missing invoice entity in payload")

            # Extract and validate payment data
            payment_data = actual_payload.get("payment", {})
            payment = payment_data.get("entity")
            if not payment:
                raise PaymentValidationError("Missing payment entity in payload")

            # Validate required fields
            invoice_id = invoice.get("id")
            payment_id = payment.get("id")
            subscription_id = invoice.get("subscription_id")

            if not all([invoice_id, payment_id, subscription_id]):
                raise PaymentValidationError(
                    "Missing required fields in invoice paid event",
                    details={
                        'has_invoice_id': bool(invoice_id),
                        'has_payment_id': bool(payment_id),
                        'has_subscription_id': bool(subscription_id)
                    }
                )

            # Find related subscription
            rzp_subs = self.db.find(
                DBColls.RAZORPAY_SUBSCRIPTIONS,
                {'id': subscription_id},
                find_one=True
            )

            if not rzp_subs:
                raise PaymentProcessingError(
                    f"Subscription not found for invoice: {invoice_id}",
                    details={'invoice_id': invoice_id, 'subscription_id': subscription_id}
                )

            plan_id = rzp_subs.get('plan_id')

            # Validate payment amount
            payment_amount = payment.get("amount", 0)
            if payment_amount <= 0:
                raise PaymentValidationError(
                    f"Invalid payment amount: {payment_amount}",
                    details={'payment_id': payment_id, 'invoice_id': invoice_id}
                )

            # Update payment and invoice records
            updated_payment = self.update_razorpay_payments(
                payment, plan_id=plan_id, set=True
            )
            set_flag = not bool(self.update_query)
            updated_invoice = self.update_razorpay_invoices(invoice, set=set_flag)

            # Process subscription payment if company ID is available
            company_id = rzp_subs.get("notes", {}).get("company_id")
            if company_id:
                try:
                    request_dict = {"company_id": company_id}
                    utility = get_dummy_utility(request_dict)
                    Sclen(utility).process_subscription_payment(
                        rzp_subs,
                        updated_invoice,
                        updated_payment
                    )

                    # Log subscription payment processing
                    self.audit_context.log_event(
                        event_type=AuditEventType.PAYMENT_COMPLETED,
                        details={
                            'subscription_payment_processed': True,
                            'company_id': company_id
                        }
                    )

                except Exception as sclen_error:
                    # Log but don't fail the webhook processing
                    self.audit_context.log_event(
                        event_type=AuditEventType.ERROR_OCCURRED,
                        details={
                            'error_type': 'subscription_payment_processing_failed',
                            'error_message': str(sclen_error),
                            'company_id': company_id
                        }
                    )
                    logger.warning(f"Failed to process subscription payment: {str(sclen_error)}")

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                payment_id=payment_id,
                amount=payment_amount / 100,  # Convert from paise to rupees
                details={
                    'invoice_id': invoice_id,
                    'subscription_id': subscription_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[INVOICE_PAID_EVENT] processed successfully for invoice {invoice_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'invoice_paid',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[INVOICE_PAID_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_invoice_expired_event(self) -> None:
        """
        Process invoice expired event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If invoice processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'invoice_expired',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            invoice_data = actual_payload.get("invoice", {})
            invoice = invoice_data.get("entity")
            if not invoice:
                raise PaymentValidationError("Missing invoice entity in payload")

            invoice_id = invoice.get("id")
            if not invoice_id:
                raise PaymentValidationError("Missing invoice ID in payload")

            # Update invoice data
            self.update_razorpay_invoices(invoice, set=True)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'event_type': 'invoice_expired',
                    'invoice_id': invoice_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[INVOICE_EXPIRED_EVENT] processed successfully for invoice {invoice_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'invoice_expired',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[INVOICE_EXPIRED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_order_paid_event(self) -> None:
        """
        Process order paid event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If order processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                details={
                    'event_type': 'order_paid',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            order_data = actual_payload.get("order", {})
            order = order_data.get("entity")
            if not order:
                raise PaymentValidationError("Missing order entity in payload")

            payment_data = actual_payload.get("payment", {})
            payment = payment_data.get("entity")
            if not payment:
                raise PaymentValidationError("Missing payment entity in payload")

            order_id = order.get("id")
            payment_id = payment.get("id")

            if not all([order_id, payment_id]):
                raise PaymentValidationError(
                    "Missing required fields in order paid event",
                    details={
                        'has_order_id': bool(order_id),
                        'has_payment_id': bool(payment_id)
                    }
                )

            # Update order data
            self.update_razorpay_orders(order, set=True)
            set_flag = not bool(self.update_query)

            # Update payment data
            self.update_razorpay_payments(payment, set=set_flag)

            # Log successful completion
            payment_amount = payment.get("amount", 0)
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                payment_id=payment_id,
                amount=payment_amount / 100 if payment_amount > 0 else 0,
                details={
                    'event_type': 'order_paid',
                    'order_id': order_id,
                    'status': 'completed'
                }
            )

            logger.info(f"[ORDER_PAID_EVENT] processed successfully for order {order_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'order_paid',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[ORDER_PAID_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_payment_failed_event(self) -> None:
        """
        Process payment failed event with enhanced validation and error handling.

        Raises:
            PaymentValidationError: If payload validation fails
            PaymentProcessingError: If payment processing fails
        """
        try:
            # Log event processing start
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_FAILED,
                details={
                    'event_type': 'payment_failed',
                    'status': 'started'
                }
            )

            # Validate payload structure
            actual_payload = self.original_payload.get("payload")
            if not actual_payload:
                raise PaymentValidationError("Missing payload in webhook data")

            payment_data = actual_payload.get("payment", {})
            payment = payment_data.get("entity")
            if not payment:
                raise PaymentValidationError("Missing payment entity in payload")

            payment_id = payment.get("id")
            if not payment_id:
                raise PaymentValidationError("Missing payment ID in payload")

            # Extract failure information
            payment_status = payment.get("status")
            error_code = payment.get("error_code")
            error_description = payment.get("error_description")

            # Update payment data
            self.update_razorpay_payments(payment, set=True)

            # Log payment failure details
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_FAILED,
                payment_id=payment_id,
                details={
                    'event_type': 'payment_failed',
                    'payment_status': payment_status,
                    'error_code': error_code,
                    'error_description': error_description,
                    'status': 'completed'
                }
            )

            logger.info(f"[PAYMENT_FAILED_EVENT] processed successfully for payment {payment_id}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'event_type': 'payment_failed',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            error_message = '[PAYMENT_FAILED_EVENT] Failed'
            logger.error(f"{error_message}: {str(e)}")

            raise PaymentProcessingError(
                f"{error_message}: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def update_razorpay_subscription(self, rzp_subs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update Razorpay subscription data with enhanced validation and error handling.

        Args:
            rzp_subs: Razorpay subscription data

        Returns:
            Dict: Updated subscription data

        Raises:
            PaymentValidationError: If subscription data is invalid
            PaymentProcessingError: If update fails
        """
        try:
            # Validate input
            if not rzp_subs or not isinstance(rzp_subs, dict):
                raise PaymentValidationError("Invalid subscription data provided")

            subscription_id = rzp_subs.get("id")
            if not subscription_id:
                raise PaymentValidationError("Missing subscription ID in data")

            # Log update start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'update_razorpay_subscription',
                    'subscription_id': subscription_id,
                    'status': 'started'
                }
            )

            query = {"id": subscription_id}
            coll = DBColls.RAZORPAY_SUBSCRIPTIONS
            existing_subs = self.db.find(coll, query, find_one=True)

            # Track changes and create update queries
            finder = Finder()
            finder.track_changes(rzp_subs, existing_subs)
            self.update_query = copy.deepcopy(finder._get_update_query())
            push_query = copy.deepcopy(finder._get_push_query())

            # Apply updates if there are changes
            if self.update_query:
                if existing_subs:
                    existing_subs.update(self.update_query)
                else:
                    existing_subs = copy.deepcopy(rzp_subs)

                # Perform database update
                self.db.update(coll, query, self.update_query, push_query=push_query)

                # Log database updates
                self._save_db_updates_in_log(
                    coll, query, self.update_query, push_query, set=True
                )

                # Log successful update
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_PROCESSED,
                    details={
                        'operation': 'update_razorpay_subscription',
                        'subscription_id': subscription_id,
                        'status': 'updated',
                        'changes_count': len(self.update_query)
                    }
                )
            else:
                # Log no changes
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_PROCESSED,
                    details={
                        'operation': 'update_razorpay_subscription',
                        'subscription_id': subscription_id,
                        'status': 'no_changes'
                    }
                )

            return existing_subs or rzp_subs

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'operation': 'update_razorpay_subscription',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            raise PaymentProcessingError(
                f"Failed to update Razorpay subscription: {str(e)}",
                details={'subscription_id': rzp_subs.get('id'), 'original_error': str(e)}
            )

    @handle_payment_exception
    def update_razorpay_payments(
        self,
        payment: Dict[str, Any],
        plan_id: Optional[str] = None,
        set: bool = False
    ) -> Dict[str, Any]:
        """
        Update Razorpay payment data with enhanced validation and error handling.

        Args:
            payment: Razorpay payment data
            plan_id: Optional plan ID for description lookup
            set: Flag for database update logging

        Returns:
            Dict: Updated payment data

        Raises:
            PaymentValidationError: If payment data is invalid
            PaymentProcessingError: If update fails
        """
        try:
            # Validate input
            if not payment or not isinstance(payment, dict):
                raise PaymentValidationError("Invalid payment data provided")

            payment_id = payment.get("id")
            if not payment_id:
                raise PaymentValidationError("Missing payment ID in data")

            # Log update start
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                payment_id=payment_id,
                details={
                    'operation': 'update_razorpay_payment',
                    'status': 'started',
                    'has_plan_id': bool(plan_id)
                }
            )

            query = {"id": payment_id}
            coll = DBColls.RAZORPAY_PAYMENTS

            # Enhance payment description if plan_id is provided
            payment_desc = payment.get("description")
            if plan_id and not payment_desc:
                try:
                    plan = self.db.find(DBColls.RAZORPAY_PLANS, {"id": plan_id}, find_one=True)
                    if plan:
                        plan_name = plan.get("item", {}).get("name")
                        if plan_name:
                            payment["description"] = plan_name

                            self.audit_context.log_event(
                                event_type=AuditEventType.PAYMENT_COMPLETED,
                                details={
                                    'operation': 'payment_description_enhanced',
                                    'plan_id': plan_id,
                                    'description': plan_name
                                }
                            )
                except Exception as plan_error:
                    logger.warning(f"Failed to enhance payment description: {str(plan_error)}")

            # Validate payment amount
            payment_amount = payment.get("amount", 0)
            if payment_amount <= 0:
                logger.warning(f"Invalid payment amount: {payment_amount} for payment {payment_id}")

            # Find existing payment
            existing_payment = self.db.find(coll, query, find_one=True)

            if not existing_payment:
                # Insert new payment
                payment_copy = copy.deepcopy(payment)
                self.db.insert(coll, [payment_copy])
                payment_copy.pop("_id", None)
                updated_payment = payment_copy

                self.audit_context.log_event(
                    event_type=AuditEventType.PAYMENT_COMPLETED,
                    payment_id=payment_id,
                    amount=payment_amount / 100 if payment_amount > 0 else 0,
                    details={
                        'operation': 'payment_inserted',
                        'status': 'new_payment'
                    }
                )
            else:
                # Update existing payment
                finder = Finder()
                finder.track_changes(payment, existing_payment)
                self.update_query = copy.deepcopy(finder._get_update_query())
                push_query = copy.deepcopy(finder._get_push_query())
                updated_payment = copy.deepcopy(existing_payment)

                # Apply updates if there are changes
                if self.update_query:
                    updated_payment.update(self.update_query)
                    self.db.update(coll, query, self.update_query, push_query=push_query)

                    self._save_db_updates_in_log(
                        coll, query, self.update_query, push_query, set=set
                    )

                    self.audit_context.log_event(
                        event_type=AuditEventType.PAYMENT_COMPLETED,
                        payment_id=payment_id,
                        details={
                            'operation': 'payment_updated',
                            'changes_count': len(self.update_query),
                            'has_push_query': bool(push_query)
                        }
                    )
                else:
                    self.audit_context.log_event(
                        event_type=AuditEventType.PAYMENT_COMPLETED,
                        payment_id=payment_id,
                        details={
                            'operation': 'payment_no_changes',
                            'status': 'unchanged'
                        }
                    )

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.PAYMENT_COMPLETED,
                payment_id=payment_id,
                details={
                    'operation': 'update_razorpay_payment',
                    'status': 'completed'
                }
            )

            return updated_payment

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'operation': 'update_razorpay_payment',
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'payment_id': payment.get('id')
                }
            )

            raise PaymentProcessingError(
                f"Failed to update Razorpay payment: {str(e)}",
                details={'payment_id': payment.get('id'), 'original_error': str(e)}
            )

    @handle_payment_exception
    def update_razorpay_invoices(
        self,
        invoice: Dict[str, Any],
        set: bool = False
    ) -> Dict[str, Any]:
        """
        Update Razorpay invoice data with enhanced validation and error handling.

        Args:
            invoice: Razorpay invoice data
            set: Flag for database update logging

        Returns:
            Dict: Updated invoice data

        Raises:
            PaymentValidationError: If invoice data is invalid
            PaymentProcessingError: If update fails
        """
        try:
            # Validate input
            if not invoice or not isinstance(invoice, dict):
                raise PaymentValidationError("Invalid invoice data provided")

            invoice_id = invoice.get("id")
            if not invoice_id:
                raise PaymentValidationError("Missing invoice ID in data")

            # Log update start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'update_razorpay_invoice',
                    'invoice_id': invoice_id,
                    'status': 'started'
                }
            )

            query = {"id": invoice_id}
            coll = DBColls.RAZORPAY_INVOICES

            # Validate invoice amount
            invoice_amount = invoice.get("amount", 0)
            if invoice_amount <= 0:
                logger.warning(f"Invalid invoice amount: {invoice_amount} for invoice {invoice_id}")

            # Find existing invoice
            existing_invoice = self.db.find(coll, query, find_one=True)

            if not existing_invoice:
                # Insert new invoice with generated invoice number
                try:
                    invoice_copy = copy.deepcopy(invoice)
                    invoice_copy["invoice_number"] = generate_sequence_no(
                        SequenceType.INVOICE_NUMBER
                    )

                    self.db.insert(coll, [invoice_copy])
                    invoice_copy.pop("_id", None)
                    updated_invoice = invoice_copy

                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'operation': 'invoice_inserted',
                            'invoice_id': invoice_id,
                            'invoice_number': invoice_copy["invoice_number"],
                            'amount': invoice_amount / 100 if invoice_amount > 0 else 0
                        }
                    )

                except Exception as insert_error:
                    raise PaymentProcessingError(
                        f"Failed to insert new invoice: {str(insert_error)}",
                        details={'invoice_id': invoice_id}
                    )
            else:
                # Update existing invoice
                finder = Finder()
                finder.track_changes(invoice, existing_invoice)
                self.update_query = copy.deepcopy(finder._get_update_query())
                push_query = copy.deepcopy(finder._get_push_query())
                updated_invoice = copy.deepcopy(existing_invoice)

                # Apply updates if there are changes
                if self.update_query:
                    updated_invoice.update(self.update_query)
                    self.db.update(coll, query, self.update_query, push_query=push_query)

                    self._save_db_updates_in_log(
                        coll, query, self.update_query, push_query, set=set
                    )

                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'operation': 'invoice_updated',
                            'invoice_id': invoice_id,
                            'changes_count': len(self.update_query),
                            'has_push_query': bool(push_query)
                        }
                    )
                else:
                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'operation': 'invoice_no_changes',
                            'invoice_id': invoice_id,
                            'status': 'unchanged'
                        }
                    )

            # Validate final invoice data
            if not updated_invoice.get("invoice_number"):
                logger.warning(f"Invoice {invoice_id} missing invoice number after update")

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'update_razorpay_invoice',
                    'invoice_id': invoice_id,
                    'status': 'completed'
                }
            )

            return updated_invoice

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'operation': 'update_razorpay_invoice',
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'invoice_id': invoice.get('id')
                }
            )

            raise PaymentProcessingError(
                f"Failed to update Razorpay invoice: {str(e)}",
                details={'invoice_id': invoice.get('id'), 'original_error': str(e)}
            )

    @handle_payment_exception
    def update_razorpay_orders(
        self,
        order: Dict[str, Any],
        set: bool = False
    ) -> Dict[str, Any]:
        """
        Update Razorpay order data with enhanced validation and error handling.

        Args:
            order: Razorpay order data
            set: Flag for database update logging

        Returns:
            Dict: Updated order data

        Raises:
            PaymentValidationError: If order data is invalid
            PaymentProcessingError: If update fails
        """
        try:
            # Validate input
            if not order or not isinstance(order, dict):
                raise PaymentValidationError("Invalid order data provided")

            order_id = order.get("id")
            if not order_id:
                raise PaymentValidationError("Missing order ID in data")

            # Log update start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'update_razorpay_order',
                    'order_id': order_id,
                    'status': 'started'
                }
            )

            query = {"id": order_id}
            coll = DBColls.RAZORPAY_ORDERS

            # Validate order amount
            order_amount = order.get("amount", 0)
            if order_amount <= 0:
                logger.warning(f"Invalid order amount: {order_amount} for order {order_id}")

            # Validate order status
            order_status = order.get("status")
            if order_status and order_status not in ["created", "attempted", "paid"]:
                logger.warning(f"Unexpected order status: {order_status} for order {order_id}")

            # Find existing order
            existing_order = self.db.find(coll, query, find_one=True)

            if not existing_order:
                # Insert new order
                try:
                    order_copy = copy.deepcopy(order)
                    self.db.insert(coll, [order_copy])
                    order_copy.pop("_id", None)
                    updated_order = order_copy

                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'operation': 'order_inserted',
                            'order_id': order_id,
                            'amount': order_amount / 100 if order_amount > 0 else 0,
                            'status': order_status
                        }
                    )

                except Exception as insert_error:
                    raise PaymentProcessingError(
                        f"Failed to insert new order: {str(insert_error)}",
                        details={'order_id': order_id}
                    )
            else:
                # Update existing order
                finder = Finder()
                finder.track_changes(order, existing_order)
                self.update_query = copy.deepcopy(finder._get_update_query())
                push_query = copy.deepcopy(finder._get_push_query())
                updated_order = copy.deepcopy(existing_order)

                # Apply updates if there are changes
                if self.update_query:
                    updated_order.update(self.update_query)
                    self.db.update(coll, query, self.update_query, push_query=push_query)

                    self._save_db_updates_in_log(
                        coll, query, self.update_query, push_query, set=set
                    )

                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'operation': 'order_updated',
                            'order_id': order_id,
                            'changes_count': len(self.update_query),
                            'has_push_query': bool(push_query)
                        }
                    )
                else:
                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'operation': 'order_no_changes',
                            'order_id': order_id,
                            'status': 'unchanged'
                        }
                    )

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'update_razorpay_order',
                    'order_id': order_id,
                    'status': 'completed'
                }
            )

            return updated_order

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'operation': 'update_razorpay_order',
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'order_id': order.get('id')
                }
            )

            raise PaymentProcessingError(
                f"Failed to update Razorpay order: {str(e)}",
                details={'order_id': order.get('id'), 'original_error': str(e)}
            )

    @handle_payment_exception
    def update_razorpay_customer(self, rzp_subs: Dict[str, Any], set: bool = False) -> Optional[Dict[str, Any]]:
        """
        Update Razorpay customer data with enhanced validation and error handling.

        Args:
            rzp_subs: Razorpay subscription data containing customer information
            set: Flag for database update logging

        Returns:
            Optional[Dict]: Updated customer data if successful, None if no customer ID

        Raises:
            PaymentValidationError: If customer data is invalid
            PaymentProcessingError: If customer update fails
        """
        try:
            # Validate customer ID
            customer_id = rzp_subs.get("customer_id")
            if not customer_id:
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_PROCESSED,
                    details={
                        'operation': 'update_razorpay_customer',
                        'status': 'skipped_no_customer_id'
                    }
                )
                return None

            # Log customer update start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'update_razorpay_customer',
                    'customer_id': customer_id,
                    'status': 'started'
                }
            )

            # Validate subscription notes
            notes = rzp_subs.get("notes", {})
            company_id = notes.get("company_id")
            if not company_id:
                raise PaymentValidationError(
                    "Missing company_id in subscription notes",
                    details={'customer_id': customer_id}
                )

            # Get company information
            request_dict = {"company_id": company_id}
            utility = get_dummy_utility(request_dict)
            sclen_inst = Sclen(utility)
            sclen_inst._get_company()

            # Prepare customer update data
            company_name = sclen_inst.company.get('name', '') if sclen_inst.company else ''
            update_data = {'name': company_name}

            # Update customer in Razorpay
            razorpay_customer, updated = self.razorpay.customer.update_customer(
                customer_id, update_data
            )

            if not updated:
                error_info = razorpay_customer.get("error", {})
                error_code = error_info.get("code", "unknown")
                error_desc = error_info.get("description", "Unknown error")

                self.audit_context.log_event(
                    event_type=AuditEventType.ERROR_OCCURRED,
                    details={
                        'operation': 'razorpay_customer_update_failed',
                        'customer_id': customer_id,
                        'error_code': error_code,
                        'error_description': error_desc
                    }
                )

                logger.error(f"Razorpay customer update failed - {error_code}: {error_desc}")
                razorpay_customer = {}

            # Save customer data if update was successful
            if razorpay_customer:
                try:
                    razorpay_customer = RazorpayCustomerSchema(**razorpay_customer).model_dump()
                    self.db.insert(DBColls.RAZORPAY_CUSTOMERS, [razorpay_customer])
                    razorpay_customer.pop("_id", None)

                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'operation': 'customer_data_saved',
                            'customer_id': customer_id
                        }
                    )
                except Exception as schema_error:
                    logger.warning(f"Failed to save customer data: {str(schema_error)}")

            # Update company with customer ID
            coll = DBColls.COMPANIES
            query = {'id': sclen_inst.company_id}
            razorpay_customer_id = razorpay_customer.get('id') or customer_id
            update_query = {'razorpay_customer_id': razorpay_customer_id}

            self.db.update(coll, query, update_query)
            self._save_db_updates_in_log(coll, query, update_query, set=set)

            # Log successful completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'update_razorpay_customer',
                    'customer_id': customer_id,
                    'company_id': company_id,
                    'status': 'completed'
                }
            )

            return razorpay_customer

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'operation': 'update_razorpay_customer',
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'customer_id': rzp_subs.get('customer_id')
                }
            )

            logger.error(f"Error while updating razorpay customer: {str(e)}")

            # Don't fail webhook processing for customer update errors
            return None

    def verify(self) -> bool:
        """
        Enhanced webhook signature verification with comprehensive security checks.

        Returns:
            bool: True if verification successful

        Raises:
            PaymentSecurityError: If signature verification fails
            PaymentValidationError: If webhook secret is not configured
        """
        try:
            # Log verification attempt
            self.audit_context.log_event(
                event_type=AuditEventType.SIGNATURE_VERIFIED,
                details={'verification_status': 'started'}
            )

            # Get webhook body and secret
            webhook_body = self.request.body.decode("utf-8")
            webhook_secret = os.getenv("WEBHOOK_SECRET") or ""

            if not webhook_secret:
                raise PaymentValidationError(
                    "WEBHOOK_SECRET is not configured",
                    details={'environment': getattr(settings, 'ENVIRONMENT_ENV', 'unknown')}
                )

            # Validate signature format
            if not self.webhook_signature:
                raise PaymentSecurityError(
                    "Missing webhook signature",
                    details={'headers_present': list(self.request_headers.keys())}
                )

            # Additional security: Check signature length
            if len(self.webhook_signature) < 10:  # Minimum reasonable signature length
                raise PaymentSecurityError(
                    "Invalid signature format",
                    details={'signature_length': len(self.webhook_signature)}
                )

            # Verify using Razorpay's utility
            try:
                self.razorpay.client.utility.verify_webhook_signature(
                    webhook_body, self.webhook_signature, webhook_secret
                )
            except Exception as razorpay_error:
                # Log the specific Razorpay error
                self.audit_context.log_event(
                    event_type=AuditEventType.SIGNATURE_FAILED,
                    details={
                        'razorpay_error': str(razorpay_error),
                        'signature_preview': self.webhook_signature[:10] + "..." if len(self.webhook_signature) > 10 else self.webhook_signature
                    }
                )

                raise PaymentSecurityError(
                    "Webhook signature verification failed",
                    details={
                        'verification_method': 'razorpay_utility',
                        'error': str(razorpay_error)
                    }
                )

            # Log successful verification
            self.audit_context.log_event(
                event_type=AuditEventType.SIGNATURE_VERIFIED,
                details={'verification_status': 'success'}
            )

            logger.info("Webhook signature verified successfully")
            return True

        except (PaymentSecurityError, PaymentValidationError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected verification error
            self.audit_context.log_event(
                event_type=AuditEventType.SIGNATURE_FAILED,
                details={
                    'verification_status': 'error',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            raise PaymentSecurityError(
                f"Webhook verification failed: {str(e)}",
                details={'original_error': str(e)}
            )

    def _set_entity_ids(self) -> Dict[str, Optional[str]]:
        """
        Extract and set entity IDs from webhook payload with enhanced validation.

        Returns:
            Dict[str, Optional[str]]: Dictionary of entity IDs extracted from payload

        Raises:
            PaymentValidationError: If payload structure is invalid
        """
        entity_ids = {}

        try:
            # Log entity ID extraction start
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_RECEIVED,
                details={
                    'operation': 'entity_id_extraction',
                    'status': 'started'
                }
            )

            # Validate payload structure
            if not isinstance(self.original_payload, dict):
                raise PaymentValidationError("Invalid payload format: expected dictionary")

            payload = self.original_payload.get("payload")
            if not payload:
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_RECEIVED,
                    details={
                        'operation': 'entity_id_extraction',
                        'status': 'no_payload'
                    }
                )
                return entity_ids

            collections = self.original_payload.get("contains", [])
            if not collections:
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_RECEIVED,
                    details={
                        'operation': 'entity_id_extraction',
                        'status': 'no_collections'
                    }
                )
                return entity_ids

            # Extract entity IDs from each collection
            for collection in collections:
                try:
                    # Validate collection name
                    if not isinstance(collection, str):
                        logger.warning(f"Invalid collection type: {type(collection)}")
                        continue

                    # Extract entity ID
                    collection_data = payload.get(collection, {})
                    entity_data = collection_data.get("entity", {})
                    entity_id = entity_data.get("id")

                    # Set entity ID as instance attribute
                    entity_key = f"{collection}_id"
                    setattr(self, entity_key, entity_id)
                    entity_ids[entity_key] = entity_id
                except Exception as collection_error:
                    # Log collection processing error but continue
                    self.audit_context.log_event(
                        event_type=AuditEventType.ERROR_OCCURRED,
                        details={
                            'operation': 'entity_id_extraction_error',
                            'collection': collection,
                            'error_message': str(collection_error)
                        }
                    )

                    logger.warning(f"Failed to extract entity ID for collection {collection}: {str(collection_error)}")

                    # Set None for failed extractions
                    entity_key = f"{collection}_id"
                    setattr(self, entity_key, None)
                    entity_ids[entity_key] = None

            # Log extraction completion
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_RECEIVED,
                details={
                    'operation': 'entity_id_extraction',
                    'status': 'completed',
                    'total_collections': len(collections),
                    'entity_keys': list(entity_ids.keys())
                }
            )

            return entity_ids

        except PaymentValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'operation': 'entity_id_extraction',
                    'error_type': type(e).__name__,
                    'error_message': str(e)
                }
            )

            logger.error(f"Unexpected error during entity ID extraction: {str(e)}")

            # Return empty dict for unexpected errors (fail-safe)
            return entity_ids

    def _get_webhook_signature(self) -> Optional[str]:
        """
        Extract webhook signature from request headers with validation.

        Returns:
            Optional[str]: Webhook signature if found

        Raises:
            PaymentSecurityError: If signature extraction fails
        """
        try:
            # Primary signature header
            webhook_signature = None

            try:
                webhook_signature = self.request_headers["X-Razorpay-Signature"]
            except KeyError:
                # Try alternative sources
                webhook_signature = self.request.META.get("HTTP_X_RAZORPAY_SIGNATURE")

                if not webhook_signature:
                    # Try other possible header variations
                    alternative_headers = [
                        "x-razorpay-signature",
                        "X_RAZORPAY_SIGNATURE"
                    ]

                    for header in alternative_headers:
                        webhook_signature = self.request_headers.get(header) or self.request.META.get(header)
                        if webhook_signature:
                            break

            if not webhook_signature:
                # Log missing signature with available headers for debugging
                available_headers = list(self.request_headers.keys())
                available_meta = [k for k in self.request.META.keys() if 'signature' in k.lower()]

                self.audit_context.log_event(
                    event_type=AuditEventType.SECURITY_EVENT,
                    details={
                        'issue': 'missing_webhook_signature',
                        'available_headers': available_headers,
                        'signature_related_meta': available_meta
                    }
                )

                raise PaymentSecurityError(
                    "Webhook signature not found in request headers",
                    details={
                        'available_headers': available_headers,
                        'checked_headers': ['X-Razorpay-Signature', 'HTTP_X_RAZORPAY_SIGNATURE']
                    }
                )

            # Basic signature format validation
            webhook_signature = webhook_signature.strip()

            # Validate signature format
            if len(webhook_signature) < 10:  # Minimum reasonable signature length
                raise PaymentSecurityError(
                    "Invalid signature format: too short",
                    details={'signature_length': len(webhook_signature)}
                )

            # Log successful signature extraction
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_RECEIVED,
                details={
                    'signature_extracted': True,
                    'signature_length': len(webhook_signature)
                }
            )

            return webhook_signature

        except PaymentSecurityError:
            # Re-raise security errors as-is
            raise
        except Exception as e:
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'error_type': 'signature_extraction_failed',
                    'error_message': str(e)
                }
            )
            raise PaymentSecurityError(
                f"Failed to extract webhook signature: {str(e)}",
                details={
                    'available_headers': list(self.request_headers.keys()),
                    'original_error': str(e)
                }
            )

    def _check_if_webhook_processed(self) -> bool:
        """
        Check if webhook has already been processed to ensure idempotency.

        Returns:
            bool: True if webhook was already processed successfully
        """
        try:
            event = self.original_payload.get("event")
            if not event:
                # If no event, can't check for duplicates
                return False

            # Determine query parameters based on event type
            query_key, query_value = "subscription_id", self.subscription_id
            if "invoice" in event:
                query_key, query_value = "invoice_id", self.invoice_id
            elif "order" in event:
                query_key, query_value = "order_id", self.entity_ids.get("order_id")

            # Skip check if we don't have the required ID
            if not query_value:
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_PROCESSED,
                    details={
                        'duplicate_check': 'skipped',
                        'reason': f'missing_{query_key}',
                        'event': event
                    }
                )
                return False

            # Primary duplicate check - by entity ID and event
            query = {
                query_key: query_value,
                "request_body.event": event,
                "error": {"$exists": False},
                "message": {"$exists": True, "$eq": "success"},
            }

            webhook_processed = self.db.find(DBColls.WEBHOOK_LOGS, query, find_one=True)
            if webhook_processed:
                self.audit_context.log_event(
                    event_type=AuditEventType.WEBHOOK_PROCESSED,
                    details={
                        'duplicate_check': 'found_by_entity',
                        'event': event,
                        'entity_id': query_value,
                        'original_processed_at': webhook_processed.get('created_on')
                    }
                )
                return True

            # Secondary duplicate check - by signature (more specific)
            if self.webhook_signature:
                query["request_headers.X-Razorpay-Signature"] = self.webhook_signature
                webhook_processed = self.db.find(DBColls.WEBHOOK_LOGS, query, find_one=True)
                if webhook_processed:
                    self.audit_context.log_event(
                        event_type=AuditEventType.WEBHOOK_PROCESSED,
                        details={
                            'duplicate_check': 'found_by_signature',
                            'event': event,
                            'entity_id': query_value,
                            'original_processed_at': webhook_processed.get('created_on')
                        }
                    )
                    return True

            # No duplicate found
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'duplicate_check': 'not_found',
                    'event': event,
                    'entity_id': query_value
                }
            )
            return False

        except Exception as e:
            # Log error but don't fail the webhook processing
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'error_type': 'duplicate_check_failed',
                    'error_message': str(e),
                    'event': self.original_payload.get('event', 'unknown')
                }
            )

            logger.warning(f"Failed to check webhook duplicate: {str(e)}")
            # Return False to allow processing (fail-safe approach)
            return False

    def _save_original_payload(self) -> Optional[str]:
        """
        Save original webhook payload for audit trail and debugging.

        Returns:
            Optional[str]: Log ID if successful, None if failed
        """
        try:
            # Sanitize sensitive data before saving
            sanitized_payload = self.security_manager.sanitize_input(self.original_payload)
            sanitized_headers = self.security_manager.sanitize_input(dict(self.request_headers))

            # Prepare log object
            obj = {
                **self.entity_ids,
                "request_body": sanitized_payload,
                "remote_address": self.request.META.get("REMOTE_ADDR", "unknown"),
                "request_headers": sanitized_headers,
                "request_method": self.request.method,
                "request_url": self.request.path,
                "env": getattr(settings, 'ENVIRONMENT_ENV', 'unknown'),
                "source": WebhookSource.RAZORPAY.name.lower(),
                "created_on": self.now,
                "webhook_event": sanitized_payload.get("event", "unknown"),
                "processing_status": "received"
            }

            # Add user agent if available
            user_agent = self.request.META.get("HTTP_USER_AGENT")
            if user_agent:
                obj["user_agent"] = user_agent[:500]  # Limit length

            # Insert into database
            self.db.insert(DBColls.WEBHOOK_LOGS, [obj])
            self.log_id = obj["_id"]

            # Log successful payload save
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_RECEIVED,
                details={
                    'payload_saved': True,
                    'log_id': str(self.log_id),
                    'event': sanitized_payload.get("event", "unknown")
                }
            )

            return self.log_id

        except Exception as e:
            # Log error but don't fail webhook processing
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'error_type': 'payload_save_failed',
                    'error_message': str(e)
                }
            )

            logger.error(f"Error while saving WEBHOOK_LOGS: {str(e)}")
            self.log_id = None
            return None

    def _save_error_msg_in_log(self, exception: Exception) -> None:
        """
        Save error information to webhook log for debugging and monitoring.

        Args:
            exception: The exception that occurred during processing
        """
        try:
            if not self.log_id:
                logger.warning("Cannot save error log: log_id is None")
                return

            # Prepare error information
            error_info = {
                "error": str(exception),
                "error_type": type(exception).__name__,
                "traceback": get_traceback(exception),
                "processing_status": "failed",
                "error_timestamp": DateUtil.get_current_timestamp()
            }

            # Add specific error details for payment exceptions
            if hasattr(exception, 'error_code'):
                error_info["error_code"] = exception.error_code.value

            if hasattr(exception, 'details'):
                error_info["error_details"] = exception.details

            # Update webhook log
            self.db.update(DBColls.WEBHOOK_LOGS, {"_id": self.log_id}, error_info)

            # Log error save
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'error_logged': True,
                    'log_id': str(self.log_id),
                    'error_type': type(exception).__name__
                }
            )

        except Exception as e:
            # Don't let logging errors break webhook processing
            logger.error(f"Error while saving error log: {str(e)}")

            # Try to log the meta-error
            try:
                self.audit_context.log_event(
                    event_type=AuditEventType.ERROR_OCCURRED,
                    details={
                        'error_type': 'error_logging_failed',
                        'original_error': str(exception),
                        'logging_error': str(e)
                    }
                )
            except Exception:
                # Last resort - just log to application logger
                logger.error(f"Failed to log webhook error: original={str(exception)}, logging_error={str(e)}")

    def _save_success_msg_in_log(self) -> None:
        """
        Save success status to webhook log for monitoring and audit.
        """
        try:
            if not self.log_id:
                logger.warning("Cannot save success log: log_id is None")
                return

            update_query = {
                "message": "success",
                "processing_status": "completed",
                "completed_on": DateUtil.get_current_timestamp()
            }

            self.db.update(DBColls.WEBHOOK_LOGS, {"_id": self.log_id}, update_query)

            # Log success save
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'success_logged': True,
                    'log_id': str(self.log_id),
                    'event': self.original_payload.get('event', 'unknown')
                }
            )

        except Exception as e:
            # Don't let logging errors break webhook processing
            logger.error(f"Error while saving success log: {str(e)}")

            # Try to log the error
            try:
                self.audit_context.log_event(
                    event_type=AuditEventType.ERROR_OCCURRED,
                    details={
                        'error_type': 'success_logging_failed',
                        'error_message': str(e)
                    }
                )
            except Exception:
                # Last resort - just log to application logger
                logger.error(f"Failed to log webhook success: {str(e)}")

    def _save_db_updates_in_log(
        self,
        coll: str,
        query: Dict[str, Any],
        update: Dict[str, Any],
        push: Dict[str, Any] = None,
        set: bool = False
    ) -> None:
        """
        Save database update operations to webhook log for audit trail and debugging.

        Args:
            coll: Database collection name
            query: Query used for the update
            update: Update operations performed
            push: Push operations performed (optional)
            set: Whether to set or push the update log
        """
        try:
            if not self.log_id:
                logger.warning("Cannot save DB updates log: log_id is None")
                return

            # Sanitize sensitive data in query and update
            sanitized_query = self.security_manager.sanitize_input(query) if query else {}
            sanitized_update = self.security_manager.sanitize_input(update) if update else {}
            sanitized_push = self.security_manager.sanitize_input(push) if push else {}

            # Prepare database update record
            db_updates = {
                "query": sanitized_query,
                "collection": coll,
                "update_query": sanitized_update,
                "timestamp": DateUtil.get_current_timestamp(),
                "operation_type": "set" if set else "push"
            }

            if sanitized_push:
                db_updates["push_query"] = sanitized_push

            # Prepare log update operations
            push_query, update_query = {}, {}
            if set:
                update_query["db_updates"] = [db_updates]
            else:
                push_query["db_updates"] = {"$each": [db_updates]}

            # Update webhook log
            self.db.update(
                DBColls.WEBHOOK_LOGS,
                {"_id": self.log_id},
                update_query,
                push_query
            )

            # Log the database update operation
            self.audit_context.log_event(
                event_type=AuditEventType.WEBHOOK_PROCESSED,
                details={
                    'operation': 'db_update_logged',
                    'collection': coll,
                    'log_id': str(self.log_id),
                    'update_type': 'set' if set else 'push',
                    'has_push_query': bool(sanitized_push)
                }
            )

        except Exception as e:
            # Log error but don't fail webhook processing
            self.audit_context.log_event(
                event_type=AuditEventType.ERROR_OCCURRED,
                details={
                    'error_type': 'db_update_logging_failed',
                    'error_message': str(e),
                    'collection': coll
                }
            )

            logger.error(f"Error while saving db updates log: {str(e)}")

            # Try to log the meta-error
            try:
                self.audit_context.log_event(
                    event_type=AuditEventType.ERROR_OCCURRED,
                    details={
                        'error_type': 'db_update_meta_logging_failed',
                        'original_error': str(e)
                    }
                )
            except Exception:
                # Last resort - just log to application logger
                logger.error(f"Failed to log DB update operation: {str(e)}")