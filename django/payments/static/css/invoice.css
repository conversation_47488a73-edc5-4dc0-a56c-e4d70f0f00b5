@page {
  size: A4;
  margin: 0;
}

body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  color: #000;
}

.invoice-container {
  width: 210mm;
  max-width: 1000px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-sizing: border-box;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.invoice-sub-container {
  margin: 10mm;
  border: 1px solid #a29292;
}

.header {
  display: flex;
  justify-content: space-between;
  padding: 15px 15px 15px 5px;
  border-bottom: 1px solid #a29292;
  align-items: stretch;
  position: relative;
}

.company-info {
  display: flex;
}

.company-logo1 {
  width: 11rem;
}

.logo-container {
  margin-right: 10px;
  width: 25%;
  color: #0066cc;
  font-weight: bold;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.logo {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo1 {
  margin-bottom: 5px;
}

.company-logo {
  width: 9rem;
}

.invoice-taxdetails-class {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: right;
}

.address {
  font-size: 12px;
  line-height: 15px;
}

.company-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.invoice-title {
  text-align: center;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.invoice-number {
  font-size: 12px;
  text-align: right;
  margin-top: 5px;
}

.invoice-details {
  display: flex;
  border-bottom: 1px solid #a29292;
}

.left-details,
.right-details {
  width: 50%;
  padding: 7px;
}

.right-details {
  border-left: 1px solid #a29292;
}

.detail-row {
  display: flex;
  margin-bottom: 5px;
}

.detail-label {
  width: 100px;
  font-size: 12px;
}

.detail-value {
  font-size: 12px;
  font-weight: bold;
}

.bill-to-title {
  font-weight: bold;
  padding: 7px;
  font-size: 12px;
}

.bill-to-address {
  font-size: 12px;
  padding: 0px 7px 5px 7px;
  line-height: 15px;
}

.invoice-table {
  width: 100%;
  border-collapse: collapse;
}

.invoice-table th {
  background-color: #f2f2f2;
  border: 1px solid #a29292;

  padding: 8px;
  font-size: 12px;
  text-align: left;
}

.invoice-table th:first-child {
  border-left: none;
}

.invoice-table th:last-child {
  border-right: none;
}

.invoice-table td:last-child {
  border-right: none;
}

.invoice-table td:first-child {
  border-left: none;
}

.invoice-table td {
  border: 1px solid #a29292;
  padding: 8px;
  font-size: 12px;
  text-align: left;
}

.summary {
  display: flex;
  border-top: 1px solid #a29292;
  border-bottom: 1px solid #a29292;
}

.total-words {
  width: 60%;
  padding: 10px;
  font-size: 12px;
  font-style: italic;
  border-right: 1px solid #a29292;
}

.total-amounts {
  width: 40%;
  padding: 0;
}

.total-row {
  display: flex;
  justify-content: space-between;
  padding: 5px 10px;
  font-size: 12px;
}

.grand-total {
  font-weight: bold;
  border-top: 1px solid #a29292;
}

.bank-details {
  padding: 10px;
  font-size: 12px;
  border-bottom: 1px solid #a29292;
}

.bank-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.notes {
  height: 6rem;
  padding: 10px;
  font-size: 12px;
  border-bottom: 1px solid #a29292;
}

.signature {
  padding: 10px 10px;
  text-align: right;
}

.signature-content {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.stamp {
  width: 80px;
  height: 80px;
  border: 1px solid #9932cc;
  border-radius: 50%;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9932cc;
  font-size: 10px;
  text-align: center;
}

.sign {
  font-style: italic;
  color: #666;
  margin-right: 20px;
}

.signature-line {
  margin-top: 5px;
  font-size: 12px;
}

.company-bottom {
  text-align: right;
  padding: 10px;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 12px;
}

.page-number {
  text-align: right;
  font-size: 10px;
  margin-top: 5px;
}

.right-align-class {
  text-align: right !important;
}

.margin-bottom-class {
  margin-bottom: 5px;
}