import logging
from rest_framework import status
from rest_framework.views import APIView
from utils import (
    format_error_response,
    format_response,
    ConfigError
)
from .webhook_utils import WebhookUtils

logger = logging.getLogger('application')


class WebhookApiView(APIView):

    def post(self, request, *args, **kwargs):
        try:
            utility = WebhookUtils(request)
            utility.run()
        except (ValueError, KeyError, AttributeError, ConfigError) as e:
            logger.error(f'[WEBHOOK_API_ERROR] {e}')
            utility._save_error_msg_in_log(e)
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                "Failed"
            )
        except Exception as e:
            logger.error(f'[WEBHOOK_API_ERROR] Unhandled exception: {e}')
            utility._save_error_msg_in_log(e)
            logger.error('Subscription webhook event failed.')
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                "Failed"
            )

        return format_response(
            status.HTTP_200_OK,
            {},
            "Success"
        )