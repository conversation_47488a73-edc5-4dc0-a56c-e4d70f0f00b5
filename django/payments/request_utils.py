import logging
from typing import Optional, Dict, Any
from django.conf import settings
from pydantic import ValidationError
from utils.constants import (
    SuccessMessages,
    ErrorMessages,
    AppDomainID,
    AppPathID
)
from utils import (
    process_payload_errors,
    Memoization
)
from .sclen import <PERSON>len
from .exceptions import (
    handle_payment_exception,
    PaymentProcessingError,
    PaymentValidationError,
    PaymentSecurityError
)
from .security import PaymentSecurityManager
from .audit import PaymentAuditLogger

logger = logging.getLogger('application')

audit_logger = PaymentAuditLogger()


class RequestUtils(object):
    """
    Enhanced Request Utility class for payment processing.

    Provides secure, auditable, and resilient payment request handling
    following modern industry standards.
    """

    def __init__(self, request):
        self._sclen = None
        self.request = request
        self.payment_link = None
        self.payments_data = None
        self.payment_failed = False
        self.already_subscribed = False
        self.is_prorated = False

        # Enhanced security and audit features
        self.security_manager = PaymentSecurityManager()
        self.audit_context = audit_logger.create_audit_context(
            user_id=getattr(request, 'user_id', None),
            company_id=getattr(request, 'company_id', None)
        )

        # Request tracking
        self.request_id = self.audit_context.request_id

        # Initialize request processing
        self._initialize_request_processing()

    def _initialize_request_processing(self):
        """Initialize request processing with security checks and audit logging."""
        try:
            # Log request initiation
            self.audit_context.log_step("request_initiated", "started")

            # Perform security checks
            self._perform_security_checks()

            # Log successful initialization
            self.audit_context.log_step("request_initialized", "completed")

        except Exception as e:
            self.audit_context.log_step("request_initialization", "failed", {
                'error': str(e)
            })
            raise

    def _perform_security_checks(self):
        """Perform initial security validations."""
        # Rate limiting check
        identifier = self._get_rate_limit_identifier()
        self.security_manager.check_rate_limit(identifier)

        # Input sanitization
        if hasattr(self.request, 'data') and self.request.data:
            sanitized_data = self.security_manager.sanitize_input(self.request.data)
            self.request.data = sanitized_data

    def _get_rate_limit_identifier(self) -> str:
        """Get identifier for rate limiting."""
        # Use company_id if available, otherwise fall back to IP
        if hasattr(self.request, 'company_id') and self.request.company_id:
            return f"company:{self.request.company_id}"

        # Get IP from request headers
        ip_address = self._get_client_ip()
        return f"ip:{ip_address}"

    def _get_client_ip(self) -> str:
        """Extract client IP address from request."""
        if hasattr(self.request, 'META'):
            # Check for forwarded IP first
            forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
            if forwarded_for:
                return forwarded_for.split(',')[0].strip()

            # Fall back to remote address
            return self.request.META.get('REMOTE_ADDR', 'unknown')

        return 'unknown'

    @property
    def sclen(self):
        if not self._sclen:
            self._sclen = Sclen(self)
        return self._sclen

    def _generate_lock_key(self):
        payload = getattr(self, 'payload', {})
        payload_ids = [payload[k] for k in payload if not isinstance(payload[k], list)]
        return f"process:{hash(frozenset(payload_ids))}"

    def _get_checkout_options(self, checkout_id: str = 'subscription_id') -> Dict[str, Any]:
        """
        Generate secure checkout options with enhanced validation.

        Args:
            checkout_id: Type of checkout identifier

        Returns:
            Dict: Checkout configuration options

        Raises:
            PaymentProcessingError: If checkout options cannot be generated
        """
        try:
            # Log checkout options generation
            self.audit_context.log_step("checkout_options_generation", "started")

            # Validate required data
            if not self.payments_data or not self.payments_data.get('id'):
                raise PaymentProcessingError(
                    "Payment data not available for checkout",
                    details={'checkout_id_type': checkout_id}
                )

            # Validate company data
            company_data = self.sclen.company
            if not company_data:
                raise PaymentProcessingError("Company data not available for checkout")

            checkout_options = {
                "name": getattr(settings, 'HOST_COMPANY', 'SCLEN.AI'),
                "key": self.sclen.razorpay.key_id,
                checkout_id: self.payments_data.get('id'),
                "description": self.get_checkout_description(),
                "callback_url": self.get_callback_url(),
                "prefill": {
                    "name": company_data.get('name', ''),
                    "email": company_data.get('email', ''),
                    "contact": str(company_data.get('phone', ''))
                },
                "theme": {
                    "color": getattr(settings, 'PAYMENT_THEME_COLOR', '#6489f9')
                },
                "method": {
                    "netbanking": True,
                    "card": True,
                    "upi": True,
                    "wallet": getattr(settings, 'ENABLE_WALLET_PAYMENTS', False),
                    "paylater": getattr(settings, 'ENABLE_PAYLATER', False)
                }
            }

            # Log successful generation
            self.audit_context.log_step("checkout_options_generation", "completed")
            return checkout_options

        except Exception as e:
            self.audit_context.log_step("checkout_options_generation", "failed", {
                'error': str(e)
            })
            raise

    @handle_payment_exception
    def validate_payload(self, validator_cls, payload_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Enhanced payload validation with security checks and audit logging.

        Args:
            validator_cls: Pydantic validator class
            payload_data: Optional payload data, uses request.data if not provided

        Returns:
            Dict: Validated payload data

        Raises:
            PaymentValidationError: If validation fails
        """
        raw_payload = payload_data or getattr(self.request, 'data', {}) or {}

        # Log validation start
        self.audit_context.log_step("payload_validation", "started")

        try:
            # Security validation first
            if isinstance(raw_payload, dict):
                self.security_manager.sanitize_input(raw_payload)

                # Validate amounts if present
                if 'amount' in raw_payload:
                    self.security_manager.validate_amount(
                        raw_payload['amount'],
                        raw_payload.get('currency', 'INR')
                    )

            # Pydantic validation
            validated_payload = validator_cls(**raw_payload).model_dump()

            # Log successful validation
            self.audit_context.log_step("payload_validation", "completed")

            # Set payload attribute if no specific data provided
            if payload_data is None:
                setattr(self, 'payload', validated_payload)

            return validated_payload

        except ValidationError as e:
            errors_list = e.errors(include_context=False, include_url=False)
            errors = process_payload_errors(errors_list)

            # Log validation failure
            self.audit_context.log_step("payload_validation", "failed", {
                'validation_errors': errors_list,
                'error_count': len(errors_list)
            })

            raise PaymentValidationError(
                message=f"Payload validation failed: {errors}",
                details={'validation_errors': errors_list}
            )
        except Exception as e:
            # Log unexpected validation error
            self.audit_context.log_step("payload_validation", "error", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })
            raise

    @handle_payment_exception
    def get_checkout_description(self) -> Optional[str]:
        """
        Generate checkout description with enhanced validation and error handling.

        Returns:
            Optional[str]: Checkout description based on payment type and plan

        Raises:
            PaymentProcessingError: If description generation fails
        """
        try:
            # Log description generation start
            self.audit_context.log_step("checkout_description_generation", "started")

            # Handle PAYG (Pay-as-you-go) descriptions
            if getattr(self.sclen, 'is_payg', False):
                payload = getattr(self, 'payload', {})
                if not payload:
                    return None

                items = payload.get('items', [])
                if not items:
                    raise PaymentValidationError("PAYG payload missing items for description generation")

                # Validate and extract item descriptions
                descriptions = []
                for i, item in enumerate(items):
                    item_description = item.get('description', '').strip()
                    descriptions.append(item_description)

                description = ', '.join(descriptions)

                # Log PAYG description generation
                self.audit_context.log_step("checkout_description_generation", "completed")

                return description

            # Handle subscription plan descriptions
            upgrade_plan = getattr(self.sclen, 'upgrade_plan', None)
            if upgrade_plan:
                # Handle plan upgrade scenarios
                if self.is_prorated:
                    try:
                        description = self.sclen._get_prorated_description(upgrade_plan)
                        return description
                    except Exception as e:
                        raise PaymentProcessingError(f"Failed to generate prorated description: {str(e)}")
                else:
                    try:
                        description = self.sclen._get_plan_name(upgrade_plan)
                        return description
                    except Exception as e:
                        raise PaymentProcessingError(f"Failed to generate upgrade plan description: {str(e)}")

            # Handle current plan description
            current_plan = getattr(self.sclen, 'current_plan', None)
            if not current_plan:
                raise PaymentProcessingError("No current plan available for description generation")

            try:
                description = self.sclen._get_plan_name(current_plan)
                return description
            except Exception as e:
                raise PaymentProcessingError(f"Failed to generate current plan description: {str(e)}")

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("checkout_description_generation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Checkout description generation failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def get_callback_url(self) -> str:
        """
        Generate secure callback URL with enhanced validation and error handling.

        Returns:
            str: Callback URL for payment processing

        Raises:
            PaymentProcessingError: If callback URL generation fails
        """
        try:
            # Log callback URL generation start
            self.audit_context.log_step("callback_url_generation", "started")

            # Set default domain and path IDs
            domain_id = AppDomainID.ADMIN
            path_id = AppPathID.ADMIN_PROFILE

            # Validate domain and path IDs
            if not domain_id or not path_id:
                raise PaymentProcessingError(
                    "Invalid domain or path ID for callback URL generation",
                    details={
                        'domain_id': str(domain_id),
                        'path_id': str(path_id)
                    }
                )

            # Get URL data from memoization
            try:
                redirect_to = Memoization.get_app_url_data(domain_id, path_id)
            except Exception as memo_error:
                raise PaymentProcessingError(
                    f"Failed to retrieve URL data from memoization: {str(memo_error)}",
                    details={
                        'domain_id': str(domain_id),
                        'path_id': str(path_id)
                    }
                )

            callback_url = redirect_to.get('url')
            if not callback_url:
                raise PaymentProcessingError("Missing URL in redirect data")

            # Validate URL format
            if not isinstance(callback_url, str) or not callback_url.strip():
                raise PaymentProcessingError("Invalid callback URL format")

            # Log successful generation
            self.audit_context.log_step("callback_url_generation", "completed")

            return callback_url

        except (PaymentProcessingError, PaymentSecurityError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("callback_url_generation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Callback URL generation failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_response_message(self) -> tuple[str, Dict[str, Any]]:
        """
        Process response message with enhanced validation and error handling.

        Returns:
            tuple: (message, redirect_to) containing response message and redirect information

        Raises:
            PaymentProcessingError: If response message processing fails
        """
        try:
            # Log response message processing start
            self.audit_context.log_step("response_message_processing", "started")

            # Initialize default redirect structure
            redirect_to = {'domain': None, 'path': None, 'url': None}
            domain_id = AppDomainID.ADMIN
            path_id = AppPathID.ADMIN_PROFILE

            # Validate domain and path IDs
            if not domain_id or not path_id:
                raise PaymentProcessingError(
                    "Invalid domain or path ID for response processing",
                    details={
                        'domain_id': str(domain_id),
                        'path_id': str(path_id)
                    }
                )

            # Determine message and redirect based on payment state
            if self.payment_failed:
                # Handle payment failure scenario
                try:
                    message = ErrorMessages.VERIFYING_PAYMENT
                    redirect_to = Memoization.get_app_url_data(domain_id, path_id)

                except Exception as failure_error:
                    raise PaymentProcessingError(
                        f"Failed to process payment failure response: {str(failure_error)}",
                        details={'original_error': str(failure_error)}
                    )

            elif self.already_subscribed:
                # Handle already subscribed scenario
                try:
                    message = SuccessMessages.ALREADY_SUBSCRIBED
                    redirect_to = Memoization.get_app_url_data(domain_id, path_id)

                except Exception as subscribed_error:
                    raise PaymentProcessingError(
                        f"Failed to process already subscribed response: {str(subscribed_error)}",
                        details={'original_error': str(subscribed_error)}
                    )
            else:
                # Handle normal payment flow
                message = SuccessMessages.PROCEED_TO_PAY

            # Log successful completion
            self.audit_context.log_step("response_message_processing", "completed")
            return message, redirect_to

        except (PaymentProcessingError, PaymentSecurityError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("response_message_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Response message processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_payments(self) -> Dict[str, Any]:
        """
        Process subscription payments with enhanced error handling and monitoring.

        Events:
            1. If `self.sclen.event` = 'subscribe' (Default Behavior - Create Subscription)
            2. If `self.sclen.event` = 'upgrade' (Upgrade Subscription)

        Returns:
            Dict: Payment processing response containing:
                - Payment Link (short_url) for default behavior
                - Full Payments Data (dict) if get_payments_data=True
                - Checkout options for frontend integration

        Raises:
            PaymentProcessingError: If subscription processing fails
        """

        try:
            # Log subscription processing start
            self.audit_context.log_step("subscription_processing", "started")

            # Process subscription through Sclen
            self.sclen.process_subscriptions()

            # Get response payload
            response = getattr(self, 'payload', {}).copy()

            # Handle already subscribed case
            if self.already_subscribed:
                return response

            # Add checkout options if requested
            if response.get('get_payments_data'):
                checkout_options = self._get_checkout_options()
                response['checkout_options'] = checkout_options
            else:
                # Add payment link for standard flow
                if not self.payment_link:
                    raise PaymentProcessingError("Payment link not generated during subscription processing")
                response['payment_link'] = self.payment_link

            self.audit_context.log_step("subscription_processing", "completed")
            return response

        except Exception as e:
            # Log processing failure
            self.audit_context.log_step("subscription_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            # Re-raise with context
            if isinstance(e, (PaymentProcessingError, PaymentValidationError, PaymentSecurityError)):
                raise
            else:
                raise PaymentProcessingError(
                    f"Subscription processing failed: {str(e)}",
                    details={'original_error': str(e)}
                )

    @handle_payment_exception
    def process_payg_payments(self) -> Dict[str, Any]:
        """
        Process PAYG (Pay-as-you-go) payments with enhanced monitoring and error handling.

        Events:
            1. If `self.sclen.event` = 'payg' (Default Behavior - Create Order)

        Returns:
            Dict: Payment processing response containing:
                - Full Payments Data (dict) if get_payments_data=True
                - Payment Link (short_url) for standard flow
                - Checkout options for frontend integration

        Raises:
            PaymentProcessingError: If PAYG processing fails
        """

        try:
            # Log PAYG processing start
            self.audit_context.log_step("payg_processing", "started")

            # Validate PAYG payload
            payload = getattr(self, 'payload', {})
            if not payload.get('items'):
                raise PaymentValidationError(
                    "PAYG items are required for processing",
                    field='items'
                )

            # Validate item amounts
            total_amount = 0
            for item in payload['items']:
                item_amount = item.get('amount', 0)
                if item_amount <= 0:
                    raise PaymentValidationError(
                        f"Invalid amount for item: {item.get('name', 'unknown')}",
                        field='items.amount'
                    )
                total_amount += item_amount

            # Security check for total amount
            self.security_manager.validate_amount(total_amount)

            # Process PAYG through Sclen
            self.sclen.process_payg()

            # Prepare response
            response = payload.copy()

            # Add checkout options if requested
            if response.get('get_payments_data'):
                checkout_options = self._get_checkout_options(checkout_id='order_id')
                response['checkout_options'] = checkout_options
            else:
                # Add payment link for standard flow
                if self.payment_link:
                    response['payment_link'] = self.payment_link

            # Log successful completion
            self.audit_context.log_step("payg_processing", "completed")
            return response

        except Exception as e:
            # Log processing failure
            self.audit_context.log_step("payg_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            # Re-raise with context
            if isinstance(e, (PaymentProcessingError, PaymentValidationError, PaymentSecurityError)):
                raise
            else:
                raise PaymentProcessingError(
                    f"PAYG processing failed: {str(e)}",
                    details={'original_error': str(e)}
                )