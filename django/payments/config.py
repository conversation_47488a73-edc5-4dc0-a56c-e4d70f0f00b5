"""
Payment Configuration Management

This module provides centralized configuration management for payment processing
with secure defaults and environment-specific settings.
"""

import os
from typing import Dict, Any
from django.conf import settings


class PaymentConfig:
    """Centralized payment configuration management."""

    # Security Settings
    PAYMENT_MAX_STRING_LENGTH = getattr(settings, 'PAYMENT_MAX_STRING_LENGTH', 1000)
    PAYMENT_MIN_AMOUNT = getattr(settings, 'PAYMENT_MIN_AMOUNT', 1.0)
    PAYMENT_MAX_AMOUNT = getattr(settings, 'PAYMENT_MAX_AMOUNT', 1000000.0)
    PAYMENT_IP_WHITELIST = getattr(settings, 'PAYMENT_IP_WHITELIST', [])

    # Rate Limiting
    PAYMENT_RATE_LIMIT_REQUESTS = getattr(settings, 'PAYMENT_RATE_LIMIT_REQUESTS', 100)
    PAYMENT_RATE_LIMIT_WINDOW = getattr(settings, 'PAYMENT_RATE_LIMIT_WINDOW', 3600)  # 1 hour

    # Audit and Logging
    PAYMENT_AUDIT_ENABLED = getattr(settings, 'PAYMENT_AUDIT_ENABLED', True)
    PAYMENT_DEBUG_LOGS = getattr(settings, 'PAYMENT_DEBUG_LOGS', False)

    # UI/UX Settings
    PAYMENT_THEME_COLOR = getattr(settings, 'PAYMENT_THEME_COLOR', '#6489f9')
    ENABLE_WALLET_PAYMENTS = getattr(settings, 'ENABLE_WALLET_PAYMENTS', False)
    ENABLE_PAYLATER = getattr(settings, 'ENABLE_PAYLATER', False)

    # Resilience Settings
    CIRCUIT_BREAKER_FAILURE_THRESHOLD = getattr(settings, 'CIRCUIT_BREAKER_FAILURE_THRESHOLD', 5)
    CIRCUIT_BREAKER_RECOVERY_TIMEOUT = getattr(settings, 'CIRCUIT_BREAKER_RECOVERY_TIMEOUT', 60)
    RETRY_MAX_ATTEMPTS = getattr(settings, 'RETRY_MAX_ATTEMPTS', 3)
    RETRY_BASE_DELAY = getattr(settings, 'RETRY_BASE_DELAY', 1.0)
    RETRY_MAX_DELAY = getattr(settings, 'RETRY_MAX_DELAY', 60.0)

    # Webhook Settings
    WEBHOOK_TIMEOUT = getattr(settings, 'WEBHOOK_TIMEOUT', 30)
    WEBHOOK_RETRY_ATTEMPTS = getattr(settings, 'WEBHOOK_RETRY_ATTEMPTS', 3)

    # Environment Settings
    ENVIRONMENT = getattr(settings, 'ENVIRONMENT_ENV', 'local')
    DEBUG_MODE = getattr(settings, 'DEBUG', False)

    @classmethod
    def get_razorpay_config(cls) -> Dict[str, Any]:
        """Get Razorpay-specific configuration."""
        return {
            'key_id': os.getenv('RAZORPAY_KEY_ID'),
            'key_secret': os.getenv('RAZORPAY_KEY_SECRET'),
            'webhook_secret': os.getenv('WEBHOOK_SECRET'),
            'test_mode': cls.ENVIRONMENT != 'production'
        }

    @classmethod
    def get_security_config(cls) -> Dict[str, Any]:
        """Get security-related configuration."""
        return {
            'max_string_length': cls.PAYMENT_MAX_STRING_LENGTH,
            'min_amount': cls.PAYMENT_MIN_AMOUNT,
            'max_amount': cls.PAYMENT_MAX_AMOUNT,
            'ip_whitelist': cls.PAYMENT_IP_WHITELIST,
            'rate_limit_requests': cls.PAYMENT_RATE_LIMIT_REQUESTS,
            'rate_limit_window': cls.PAYMENT_RATE_LIMIT_WINDOW
        }

    @classmethod
    def get_resilience_config(cls) -> Dict[str, Any]:
        """Get resilience pattern configuration."""
        return {
            'circuit_breaker': {
                'failure_threshold': cls.CIRCUIT_BREAKER_FAILURE_THRESHOLD,
                'recovery_timeout': cls.CIRCUIT_BREAKER_RECOVERY_TIMEOUT
            },
            'retry': {
                'max_attempts': cls.RETRY_MAX_ATTEMPTS,
                'base_delay': cls.RETRY_BASE_DELAY,
                'max_delay': cls.RETRY_MAX_DELAY
            }
        }

    @classmethod
    def get_audit_config(cls) -> Dict[str, Any]:
        """Get audit and logging configuration."""
        return {
            'enabled': cls.PAYMENT_AUDIT_ENABLED,
            'debug_logs': cls.PAYMENT_DEBUG_LOGS,
            'environment': cls.ENVIRONMENT
        }

    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """
        Validate payment configuration and return validation results.

        Returns:
            Dict: Validation results with any issues found
        """
        issues = []
        warnings = []

        # Check required environment variables
        required_env_vars = ['RAZORPAY_KEY_ID', 'RAZORPAY_KEY_SECRET']
        for var in required_env_vars:
            if not os.getenv(var):
                issues.append(f"Missing required environment variable: {var}")

        # Check webhook secret in production
        if cls.ENVIRONMENT in ['prod', 'beta'] and not os.getenv('WEBHOOK_SECRET'):
            issues.append("WEBHOOK_SECRET is required in production environment")

        # Validate amount limits
        if cls.PAYMENT_MIN_AMOUNT >= cls.PAYMENT_MAX_AMOUNT:
            issues.append("PAYMENT_MIN_AMOUNT must be less than PAYMENT_MAX_AMOUNT")

        # Check rate limiting settings
        if cls.PAYMENT_RATE_LIMIT_REQUESTS <= 0:
            issues.append("PAYMENT_RATE_LIMIT_REQUESTS must be positive")

        if cls.PAYMENT_RATE_LIMIT_WINDOW <= 0:
            issues.append("PAYMENT_RATE_LIMIT_WINDOW must be positive")

        # Check resilience settings
        if cls.CIRCUIT_BREAKER_FAILURE_THRESHOLD <= 0:
            warnings.append("CIRCUIT_BREAKER_FAILURE_THRESHOLD should be positive")

        if cls.RETRY_MAX_ATTEMPTS <= 0:
            warnings.append("RETRY_MAX_ATTEMPTS should be positive")

        # Security warnings
        if cls.ENVIRONMENT == 'prod':
            if cls.DEBUG_MODE:
                warnings.append("DEBUG mode is enabled in production")

        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings
        }

    @classmethod
    def get_feature_flags(cls) -> Dict[str, bool]:
        """Get feature flag configuration."""
        return {
            'audit_enabled': cls.PAYMENT_AUDIT_ENABLED,
            'debug_logs': cls.PAYMENT_DEBUG_LOGS,
            'wallet_payments': cls.ENABLE_WALLET_PAYMENTS,
            'paylater': cls.ENABLE_PAYLATER,
            'ip_whitelist_enabled': bool(cls.PAYMENT_IP_WHITELIST)
        }


# Global configuration instance
payment_config = PaymentConfig()


def get_payment_config() -> PaymentConfig:
    """Get the global payment configuration instance."""
    return payment_config


def validate_payment_setup() -> Dict[str, Any]:
    """
    Validate the complete payment setup.

    Returns:
        Dict: Comprehensive validation results
    """
    config_validation = payment_config.validate_config()

    # Additional runtime checks
    runtime_checks = {
        'database_connection': True,  # Would check actual DB connection
        'redis_connection': True,     # Would check actual Redis connection
        'razorpay_api_access': True   # Would test Razorpay API access
    }

    return {
        'config_validation': config_validation,
        'runtime_checks': runtime_checks,
        'overall_status': config_validation['valid'] and all(runtime_checks.values())
    }


# Configuration constants for easy access
SECURITY_CONFIG = payment_config.get_security_config()
RESILIENCE_CONFIG = payment_config.get_resilience_config()
AUDIT_CONFIG = payment_config.get_audit_config()
FEATURE_FLAGS = payment_config.get_feature_flags()
