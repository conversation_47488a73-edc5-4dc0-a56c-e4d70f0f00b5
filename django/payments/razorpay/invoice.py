import logging

logger = logging.getLogger('application')


class Invoice(object):
    """
    Create / Update / Retrieve / Issue / Cancel / Delete / Notify Invoice
    """

    def __init__(self, client):
        self.client = client

    def get_invoices(self, invoice_id=None, sub_id=None, limit=10, offset=0):
        if invoice_id:
            invoice_data = self.client.invoice.fetch(invoice_id)
            return invoice_data, 'error' not in invoice_data

        options = {
            'count': limit,
            'skip': offset
        }

        if sub_id:
            options['subscription_id'] = sub_id

        invoice_data = self.client.invoice.all(options)
        return invoice_data, 'error' not in invoice_data

    def create_invoice(self, data):
        invoice_data = self.client.invoice.create(data)
        return invoice_data, 'error' not in invoice_data

    def update_invoice(self, invoice_id, data):
        invoice_data = self.client.invoice.edit(invoice_id, data)
        return invoice_data, 'error' not in invoice_data

    def issue_invoice(self, invoice_id):
        invoice_data = self.client.invoice.issue(invoice_id)
        return invoice_data, 'error' not in invoice_data

    def delete_invoice(self, invoice_id):
        invoice_data = self.client.invoice.delete(invoice_id)
        return invoice_data

    def cancel_invoice(self, invoice_id):
        invoice_data = self.client.invoice.cancel(invoice_id)
        return invoice_data

    def notify_invoice(self, invoice_id, medium='email'):
        """
            Summary:
                Notify invoice to the customer

            Args:
                invoice_id: str
                medium: str

            Possible mediums:
                `sms`
                `email`

            Response (dict):
                A dict containing:
                    success: boolean (True or False)
        """
        notified = self.client.invoice.notify_by(invoice_id, medium)
        return notified