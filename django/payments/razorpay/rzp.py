import logging
from .base import Ra<PERSON>payBase
from .customer import Customer
from .invoice import Invoice
from .item import Item
from .order import Order
from .payment import Payment
from .plan import Plan
from .plinks import PaymentLink
from .subscription import Subscription

logger = logging.getLogger('application')


class Razorpay(RazorpayBase):

    def __init__(self):
        super(Razorpay, self).__init__()
        self._customer = None
        self._invoice = None
        self._order = None
        self._item = None
        self._plan = None
        self._payment = None
        self._payment_link = None
        self._subscription = None

    @property
    def customer(self):
        if not self._customer:
            self._customer = Customer(self.client)
        return self._customer

    @property
    def invoice(self):
        if not self._invoice:
            self._invoice = Invoice(self.client)
        return self._invoice

    @property
    def order(self):
        if not self._order:
            self._order = Order(self.client)
        return self._order

    @property
    def item(self):
        if not self._item:
            self._item = Item(self.client)
        return self._item

    @property
    def plan(self):
        if not self._plan:
            self._plan = Plan(self.client)
        return self._plan

    @property
    def payment(self):
        if not self._payment:
            self._payment = Payment(self.client)
        return self._payment

    @property
    def payment_link(self):
        if not self._payment_link:
            self._payment_link = PaymentLink(self.client)
        return self._payment_link

    @property
    def subscription(self):
        if not self._subscription:
            self._subscription = Subscription(self.client)
        return self._subscription