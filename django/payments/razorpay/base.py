import razorpay
from django.conf import settings
from utils.memoization import Memoization


class RazorpayBase(object):

    def __init__(self):
        self.saas_settings = Memoization.get_saas_settings()
        self.test_live_mode = self.saas_settings['test_live_mode']
        if ((settings.ENVIRONMENT_ENV == 'prod') or self.test_live_mode):
            self.key_secret = self.saas_settings['rzp_key_secret']
            self.key_id = self.saas_settings['rzp_key_id']
        else:
            self.key_secret = self.saas_settings['test_rzp_key_secret']
            self.key_id = self.saas_settings['test_rzp_key_id']

        self.client = razorpay.Client(auth=(self.key_id, self.key_secret))
        self.client.set_app_details(
            {
                "title": "Razorpay - SaaS Plans & Subscriptions",
                "version": "v1.0.0"
            }
        )