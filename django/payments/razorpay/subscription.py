import logging
from schema import (
    CreateRazorpaySubscriptionAddonSchema,
    CreateRazorpaySubscriptionSchema
)

logger = logging.getLogger('application')


class Subscription(object):
    """
        1. Create Subscription
        2. Update Subscription
        3. Get Subscription
        4. Create Subscription With Payment Link
        5. Cancel Subscription
        6. Pause Subscription
        7. Resume Subscription
        8. Get Subscription Pending Update
        9. Cancel Subscription Pending Update
        10. Get Subscription Invoices
        11. Create Subscription With Offer
        12. Delete Offer Linked With Subscription
    """

    def __init__(self, client):
        self.client = client

    def get_subscription_addon_schema(self):
        return CreateRazorpaySubscriptionAddonSchema().model_dump()

    def get_subscription_schema(self):
        """ Razorpay create subscription payload """
        return CreateRazorpaySubscriptionSchema().model_dump()

    def get_subscriptions(self, sub_id=None, limit=10, offset=0):
        if sub_id:
            subscription_data = self.client.subscription.fetch(sub_id)
            return subscription_data, 'error' not in subscription_data

        options = {'count': limit, 'skip': offset}
        subscription_data = self.client.subscription.all(options)
        return subscription_data, 'error' not in subscription_data

    def create_subscription(self, data):
        subscription_data = self.client.subscription.create(data)
        return subscription_data, 'error' not in subscription_data

    def update_subscription(self, sub_id, data):
        subscription_data = self.client.subscription.edit(sub_id, data)
        return subscription_data, 'error' not in subscription_data

    def create_subscription_with_plink(self):
        raise NotImplementedError('Not Implemented yet.')

    def cancel_subscription(self, sub_id):
        """
            0: Cancel subscription immediately
            1: Cancel subscription at the end of the current billing cycle
        """
        data = {'cancel_at_cycle_end': 0}
        subscription_data = self.client.subscription.cancel(sub_id, data)
        return subscription_data, 'error' not in subscription_data

    def pause_subscription(self, sub_id):
        data = {"pause_at": "now"}
        subscription_data = self.client.subscription.pause(sub_id, data)
        return subscription_data, 'error' not in subscription_data

    def resume_subscription(self, sub_id):
        data = {"resume_at": "now"}
        subscription_data = self.client.subscription.resume(sub_id, data)
        return subscription_data, 'error' not in subscription_data

    def get_pending_update(self):
        raise NotImplementedError('Not Implemented yet.')

    def cancel_update(self):
        raise NotImplementedError('Not Implemented yet.')

    def get_subscription_invoices(self, sub_id):
        options = {'subscription_id': sub_id}
        invoice_data = self.client.invoice.all(options)
        return invoice_data, 'error' not in invoice_data

    def create_subscription_with_offer(self):
        raise NotImplementedError('Not Implemented yet.')

    def delete_offer_linked_with_subscription(self):
        raise NotImplementedError('Not Implemented yet.')