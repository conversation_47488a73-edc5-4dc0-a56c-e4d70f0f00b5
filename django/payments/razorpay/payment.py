import logging

logger = logging.getLogger('application')


class Payment(object):
    """
        Retrieve Payments
    """

    def __init__(self, client):
        self.client = client

    def get_payments(self, pay_id=None, limit=10, offset=0):
        if pay_id:
            payment_data = self.client.payment.fetch(pay_id)
            return payment_data, 'error' not in payment_data

        options = {'count': limit, 'skip': offset}
        payment_data = self.client.payment.all(options)
        return payment_data, 'error' not in payment_data