import logging
from schema import CreateRazorpayPlanSchema

logger = logging.getLogger('application')


class Plan(object):
    """
    Create / Retrieve Plans
    """

    def __init__(self, client):
        self.client = client

    def get_create_plan_schema(self):
        return CreateRazorpayPlanSchema().model_dump()

    def create_plan(self, data):
        plan_data = self.client.plan.create(data)
        return plan_data, 'error' not in plan_data

    def get_plans(self, plan_id=None, limit=10, offset=0):
        if plan_id:
            plan_data = self.client.plan.fetch(plan_id)
            return plan_data, 'error' not in plan_data

        options = {'count': limit, 'skip': offset}
        plan_data = self.client.plan.all(options)
        return plan_data, 'error' not in plan_data