import logging
from schema import CreateRazorpayCustomerSchema

logger = logging.getLogger('application')


class Customer(object):
    """
    Create / Update / Retrieve Customer & (Add / Delete) Customer Bank Account
    """

    def __init__(self, client):
        self.client = client

    def get_create_customer_schema(self):
        return CreateRazorpayCustomerSchema().model_dump()

    def get_customers(self, customer_id=None, limit=10, offset=0):
        """
        Fetch Customer Details from Razorpay.

        Args:
            customer_id (str or None): The Razorpay Customer ID.

        Returns:
            Tuple[dict, bool]:
                A tuple containing id customer_id is not None:
                - customer_data (dict): The customer details.
                - (bool): True or False indicating success or failure.

            Tuple[list, bool] if customer_id is None:
                A tuple containing:
                - customer_data (list): The list of all the customer details.
                - (bool): True or False indicating success or failure.
        """

        if customer_id:
            customer_data = self.client.customer.fetch(customer_id)
            return customer_data, 'error' not in customer_data

        options = {'count': limit, 'skip': offset}
        customer_data = self.client.customer.all(options)
        return customer_data, 'error' not in customer_data

    def create_customer(self, data):
        """
        Create Customer in Razorpay.

        Args:
            data (dict): The customer data (payload).

        Returns:
            Tuple[dict, bool]:
                A tuple containing:
                - customer_data (dict): The customer details.
                - (bool): True or False indicating success or failure.

        """
        customer_data = self.client.customer.create(data)
        return customer_data, 'error' not in customer_data

    def update_customer(self, customer_id, data):
        """
        Update Customer in Razorpay

        Args:
            customer_id (str): The Razorpay customer ID.
            data (dict): The customer data to be updated (payload).

        Returns:
            Tuple[dict, bool]:
                A tuple containing:
                - customer_data (dict): The updated customer details.
                - (bool): True or False indicating success or failure

        """
        customer_data = self.client.customer.edit(customer_id, data)
        return customer_data, 'error' not in customer_data

    def add_customer_bank_account(self, customer_id, data):
        """
        Add Customer Bank Account in Razorpay

        Args:
            customer_id (str): The Razorpay Customer ID
            data (dict): The bank details of the customer.

        Returns:
            Tuple[dict, bool]:
                A tuple containing:
                - customer_data (dict): The customer data after addition of bank account.
                - (bool): True or False indicating success or failure
        """
        customer_data = self.client.customer.addBankAccount(customer_id, data)
        # {
        #     # "ifsc_code": "UTIB0000194",
        #     # "account_number": "***************",
        #     # "beneficiary_name": "Pratheek",
        #     # "beneficiary_address1": "address 1",
        #     # "beneficiary_address2": "address 2",
        #     # "beneficiary_address3": "address 3",
        #     # "beneficiary_address4": "address 4",
        #     # "beneficiary_email": "<EMAIL>",
        #     # "beneficiary_mobile": "**********",
        #     # "beneficiary_city": "Bangalore",
        #     # "beneficiary_state": "KA",
        #     # "beneficiary_country": "IN"
        # }
        return customer_data, 'error' not in customer_data

    def delete_customer_bank_account(self, customer_id, bank_account_id):
        """
        Delete Customer Bank Account from Razorpay

        Args:
            customer_id (str): The Razorpay Customer ID.
            bank_account_id (str): The bank account id generated durin Add Customer Bank Account.

        Returns:
            Tuple[dict, bool]:
                A tuple containing:
                - bank_details (dict): The bank details with status: deleted.
                - (bool): True or False indicating success or failure
        """
        bank_details = self.client.customer.deleteBankAccount(customer_id, bank_account_id)
        return bank_details, 'error' not in bank_details