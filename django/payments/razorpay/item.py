import logging

logger = logging.getLogger('application')


class Item(object):
    """
    Create / Update / Retrieve Items
    """

    def __init__(self, client):
        self.client = client

    def get_items(self, item_id=None, limit=10, offset=0):
        if item_id:
            item_data = self.client.item.fetch(item_id)
            return item_data, 'error' not in item_data

        options = {'count': limit, 'skip': offset}
        item_data = self.client.item.all(options)
        return item_data, 'error' not in item_data

    def create_item(self, data):
        item_data = self.client.item.create(data)
        return item_data, 'error' not in item_data

    def update_item(self, item_id, data):
        item_data = self.client.item.edit(item_id, data)
        return item_data, 'error' not in item_data