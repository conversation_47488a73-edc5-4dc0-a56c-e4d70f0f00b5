import logging
from schema import CreateRazorpayOrderSchema

logger = logging.getLogger('application')


class Order(object):
    """
    Create / Update / Retrieve Items
    """

    def __init__(self, client):
        self.client = client

    def get_create_order_schema(self):
        return CreateRazorpayOrderSchema().model_dump()

    def get_orders(self, order_id=None, limit=10, offset=0):
        if order_id:
            order_data = self.client.order.fetch(order_id)
            return order_data, 'error' not in order_data

        options = {'count': limit, 'skip': offset}
        order_data = self.client.order.all(options)
        return order_data, 'error' not in order_data

    def create_order(self, data):
        order_data = self.client.order.create(data)
        return order_data, 'error' not in order_data

    def update_order(self, order_id, data):
        order_data = self.client.order.edit(order_id, data)
        return order_data, 'error' not in order_data