
# Subscribe, Upgrade & Generate Subscription Payment Link Flow & Steps

```py
pip install razorpay
```

## API Details
- **Endpoint**: `payments/api/v1/payments/payment-link/`
- **Content-Type**: `application/json`
- **Method**: `POST`

### `[SUBSCRIBE]` Payload Example 
```json
{
    "current_subscription_id": "<subscription_id>",
    "event": "subscribe"
}
```
### `[UPGRADE]` Payload Example 
```json
{
    "current_subscription_id": "<subscription_id>",
    "event": "upgrade",                   
    "new_plan_id": "<new_plan_id>",
}
```
### `[UPGRADE WITH ADDONS]` Payload Example 
```json
{
    "current_subscription_id": "<subscription_id>",
    "event": "upgrade",                   
    "new_plan_id": "<new_plan_id>",
    "addons": [ 
        {
            "id": "<addon_id>",
            "qty": 2 // default = 1
        }
    ]
}
```

### Request Body Field Definition
  - `current_subscription_id`: SCLEN Subscription ID 
  - `event`: Event (`subscribe` or `upgrade`)
  - `new_plan_id`: SCLEN New Plan ID (Required; when `event : upgrade`)
  - `addons`: Addons [`type : list of dicts`] (Optional) (Only Applicable when `event : upgrade`)
    - `id`: Addon ID
    - `qty`: Number of Addon Quantity

### Response
```json
{
  "payment_link": "https://rzp.io/rzp/XU7OYSKg"
}
```

---

## Step 1: Create `[Subscription]`
- Use `current_subscription_id` to create subscription in Razorpay using Razorpay APIs.
```py
rzp_sub, created = self.razorpay.subscription.create_subscription(sub_data)
if not created:
    error_code = rzp_sub['error']['code']
    error_desc = rzp_sub['error']['description']
    raise ValueError(f"{error_code}: {error_desc}")
```
### Payload
```json
{
    "plan_id": "<plan_id>",
    "customer_notify": true,
    "quantity": 1,
    "total_count": 12,
    "expire_by": 1745913850,
    "addons": [], // (if any)
    "notes": {
      "user_id": "<user.id>",
      "company_id": "<company.id>",
      "subscription_id": "<current_subscription_id>"
    },
    "notify_info": {
      "notify_phone": "<company.phone>",
      "notify_email": "<company.email>"
    }
}
```

### Response
```json
{
    "id": "sub_PpBcogI26ytZda",
    "entity": "subscription",
    "plan_id": "plan_PpBUyffrJx3WUj",
    "status": "created",
    "current_start": null,
    "current_end": null,
    "ended_at": null,
    "quantity": 1,
    "notes": {
        "user_id": "751057a456674e60bfac92f6b0cd30ad",
        "company_id": "84d25e6b1fea4d66a4fed5c0f415dd81"
    },
    "charge_at": null,
    "start_at": null,
    "end_at": null,
    "auth_attempts": 0,
    "total_count": 12,
    "paid_count": 0,
    "customer_notify": true,
    "created_at": 1738137851,
    "expire_by": 1745913850,
    "short_url": "https://rzp.io/rzp/XU7OYSKg",
    "has_scheduled_changes": false,
    "change_scheduled_at": null,
    "source": "api",
    "remaining_count": 11
}
```
---

## Step 2: Upgrade `[Subscription]`
- Follow `Step 1`
- Cancel Previous Subscription
```py
self.razorpay.subscription.cancel_subscription(current_razorpay_sub_id)
```
- Fetch Previous Subscription Invoice & Create
```py
subscription_invoices, fetched = self.razorpay.invoice.get_invoices(sub_id=sub_id)
```

---

## Step 3: Configure `[Webhooks]` For Different Events
- Configure Webhook Endpoints with secret key & events on Razorpay Dashboard.
- Verify the webhook signature using Razorpay's Verify Webhook Signature API.
```py
/* Python SDK: https://github.com/razorpay/razorpay-python */
import razorpay
client = razorpay.Client(auth=("[YOUR_KEY_ID]", "[YOUR_KEY_SECRET]"))

client.utility.verify_webhook_signature(webhook_body, webhook_signature, webhook_secret)
#webhook_body should be raw webhook request body
```
- **X-Razorpay-Signature**: The hash signature is calculated using HMAC with SHA256 algorithm; with your webhook secret set as the key and the webhook request body as the message.
- **Behind the Scenes**:
```py
key                = webhook_secret
message            = webhook_body // raw webhook request body
received_signature = webhook_signature

expected_signature = hmac('sha256', message, key)

if expected_signature != received_signature
    throw SecurityError
end
```
- **Note**:
  - While generating the signature at your end, ensure that the webhook body passed as an argument is the raw webhook request body. Do not parse or cast the webhook request body.
- **Idempotency**:
  - There could be scenarios where your endpoint might receive the same webhook event multiple times. 
  This is an expected behaviour based on the webhook design.

  - To handle duplicate webhook events:
      - You can identify the duplicate webhooks using the x-razorpay-event-id header. 
      The value for this header is unique per event.

      - Check the value of x-razorpay-event-id in the webhook request header.
      - Verify if an event with the same header is processed at your end.

- **Order of Webhooks**:
    - Ideally, you should receive a webhook in the order in which the webhook events occur. 
    However, you may not always receive the webhooks in the order.

    - **Example - Payments**:
      - For example, in the case of a payment, you should receive webhooks in the following order:
          - `payment.authorized`
          - `payment.captured`

      - The above order may not be followed at all times. You should configure your 
      webhook URL to not expect delivery of these events in this order and handle 
      such scenarios.

- Webhook Event Types (As per our requirement):
  - `subscription.authenticated`
  - `subscription.activated`
  - `subscription.charged`
  - `subscription.paused`
  - `subscription.resumed`
  - `subscription.cancelled`
  - `subscription.pending`
  - `subscription.halted`
  - `invoice.paid`
  - `invoice.expired`

- Subscription Related Webhook Events:
  - Endpoint: `payments/api/v1/payments/h/s/wh/` (Handles Subscription Webhook Events)
  - Events Handled:
    - `subscription.authenticated`
    - `subscription.activated`
    - `subscription.charged`
    - `subscription.paused`
    - `subscription.resumed`
    - `subscription.cancelled`

- Invoice Related Webhook Events:
  - Endpoint: `payments/api/v1/payments/h/i/wh/` (Handles Invoice Webhook Events)
  - Events Handled:
    - `invoice.paid`
    - `invoice.expired`

Subscription Flow Ends Here

---

# Optional Razorpay APIs

## How to Create a `[Payment Link]`

- **Types of Payment Link APIs**:
  - Standard Payment Link
  - UPI Payment Link

- **Response**:
  - Payment link details (dict), including:
    - `id` (Payment Link ID).
    - `callback_url` (if specified).
    - `expire_by` (Unix timestamp).
      - Timestamp, in Unix (secs), at which the [Payment Link] will expire. By default, a [Payment Link] will be valid for six months from the date of creation. Please note that the expire by date cannot exceed more than six months from the date of creation.
    - `reference_id` (Unique for each Payment Link, max 40 characters).
      - Reference number tagged to a [Payment Link]. Must be a unique number for each [Payment Link]. The maximum character limit supported is 40.
    - `short_url` (redirects to the Razorpay hosted payment page).
      - The unique short URL generated for the [Payment Link]. Which will redirect the user to the Razorpay hosted payment page for payment.
    - Other [Payment Link] details 
    - (For more info, please see payments/documents/payment_links.md)

    - If a `callback_url` is specified, adds a redirect URL to the [Payment Link].
    - Once the customer completes the payment, they are redirected to the specified URL.

    - The following parameters are appended as URL Params in the `callback_url` after successful payment completion:
      - For example:
          - `razorpay_payment_id=pay_Pl2aK0tlMGb8t2`
          - `razorpay_payment_link_id=plink_Pl0uHbVpOdHz71`
          - `razorpay_payment_link_reference_id=4703ef3cbce04a409d49c6948b8ffab3`
            - This is nothing but the unique `reference_id` added during payment link payload creation.
          - `razorpay_payment_link_status=paid`
          - `razorpay_signature=16e9ab267287e51ff55c9ba5b9cc925747688dc4487d533538c8df620e60505d`
    
    - `razorpay_signature` must be verified 
    - (For more info, please see payments/documents/payments_verification.md)

- **Custom Payment Link Options**:
  - The following options are available to modify Razorpay's hosted payment page.
    ```json
    // Example Payload:
    {
        "amount": 1000,
        "currency": "INR",
        ...
        ...

        "options": {
          "checkout": {
            "theme": {
              // Implement Thematic Changes in Payment Links Checkout Section
              // Use this payload to modify the top bar theme element of the Checkout 
              // UI on the payment request page. This restricts customers from navigating 
              // to the initial screen of Checkout and selecting a different payment method.
              "hide_topbar": true,

              // Change Business Name
              // Use this payload to change the business name that appears on the 
              // Checkout section of the Payment Link's payment request page.
              "name": "Coffee Beans Suppliers"
            },

            // Customise Payment Methods - Options and Method Parameters
            // Use this payload to enable or disable display of specific payment methods. 
            // For example, you can use the options and method parameters to display only 
            // card and netbanking methods on the Checkout.
            // You can use the options parameter to display or hide any of the payment methods:
            // Card, Netbanking, UPI, Wallet
            "method": {
              "netbanking": true,
              "card": true,
              "upi": true,
              "wallet": false
            },

            // Customise Payment Methods - Options and Config Parameters
            // You can configure the payment methods of your choice on the Checkout section of 
            // the Payment Links to provide a highly personalised experience for your customers.

            // You can use the options and config parameters for greater control over display 
            // of specific payment methods or instruments on the Checkout section of Payment Link. 
            // For example, you can remove a certain bank from Netbanking or highlight a specific wallet.
            "config": {
              "display": {
                "blocks": {
                  "banks": {
                    "name": "All Payment Methods",
                    "instruments": [
                      {
                        "method": "upi"
                      },
                      {
                        "method": "netbanking"
                      },
                      {
                        "method": "card"
                      },
                      {
                        "method": "wallet"
                      }
                    ]
                  }
                },
                "sequence": [
                  "block.banks"
                ],
                "preferences": {
                  "show_default_blocks": false
                }
              }
            }
          }
        }
    }
    ```
    - (For more info, please see payments/documents/payment_links.md)

---
## `[IMPORTANT NOTE]` ATTEMPT DIRECT UPGRADE
  - The below update subscription flow from RAZORPAY is `NOT` suitable for our requirement.
  - We are going with cancel & create new subscription instead
```py
def upgrade_subscription(self, new_plan, current_subscription, current_razorpay_sub_id):
  razorpay_subscription, upgraded = self.attempt_direct_upgrade(current_razorpay_sub_id)
  if upgraded:
      razorpay_subscription = self.finalise_upgrade(
          new_plan,
          current_subscription,
          razorpay_subscription,
          upgraded=upgraded
      )
      return razorpay_subscription

def attempt_direct_upgrade(self, current_razorpay_sub_id):
    try:
        update_data = {
            'plan_id': self.sclen.razorpay_new_plan_id,
            'schedule_change_at': "now",
            'customer_notify': True,
            # 'remaining_count': 0
        }
        razorpay_subscription, upgraded = self.razorpay.subscription.update_subscription(
            current_razorpay_sub_id, update_data
        )
    except BadRequestError as e:
        logger.error(
            f'[RAZORPAY ERROR: {str(e)}] - Skipping and creating '
            'new subscription with upgrade plan details.'
        )
    return razorpay_subscription, upgraded
```