import logging
from django.conf import settings
from schema import CreatePaymentLinkSchema

logger = logging.getLogger('application')


class PaymentLink(object):
    """
        1. Create Standard Paymment Link
        2. Create UPI Paymment Link
        3. Get Standard Payment Links / Get Standard Payment Link By ID
        4. Get UPI Payment Link / Get UPI Payment Link By ID
        5. Notify customer via `sms` & `email`
        6. Update Standard Payment Link
        7. Update UPI Payment Link
        8. Cancel Standard Payment Link
        9. Cancel Standard Payment Link
    """

    def __init__(self, client):
        self.client = client
        self.callback_enabled = getattr(settings, 'CALLBACK_URL_ENABLED', True)
        self.default_callback_url = getattr(
            settings,
            'DEFAULT_CALLBACK_URL',
            'http://localhost:5000/payments/api/v1/payments/complete-payment/'
        )

    def get_plink_schema(self):
        return CreatePaymentLinkSchema().model_dump()

    def create_standard_plink(self, plink_data):
        """
        Create Standard Payment Link

        Args:
            plink_data (dict): The payment link creation data.

        Returns:
            Tuple[dict, bool]:
                A tuple containing:
                - payment_link (dict): payment link details or error object
                - (bool): True or False indicating success or failure
        """
        if self.callback_enabled and not plink_data.get('callback_url'):
            if self.default_callback_url:
                plink_data.update({
                    'callback_url': self.default_callback_url,
                    'callback_method': 'get'  # default, no other method applicable
                })

        payment_link = self.client.payment_link.create(plink_data)
        return payment_link, 'error' not in payment_link

    def create_upi_plink(self, plink_data):
        """
            Create UPI Payment Link

        Args:
            plink_data (dict): The payment link creation data with upi_link: True.

        Returns:
            Tuple[dict, bool]:
                A tuple containing:
                - payment_link (dict): payment link details or error object
                - (bool): True or False indicating success or failure
        """
        if self.callback_enabled and not plink_data.get('callback_url'):
            if self.default_callback_url:
                plink_data.update({
                    'callback_url': self.default_callback_url,
                    'callback_method': 'get'  # default, no other method applicable
                })

        payment_link = self.client.payment_link.create(plink_data)
        return payment_link, 'error' not in payment_link

    def get_standard_plinks(self, std_plink_id=None, limit=10, offset=0):
        """
            Fetch Standard Payment Link by ID
                - client.payment_link.fetch(paymentLinkId)

            Fetch All Standard Payment Links
                - client.payment_link.all()

            Args:
                std_plink_id (str or None): The payment link id.

            Returns:
                Tuple[dict, bool]:
                    A tuple containing:
                    - payment_link (dict): payment link details or error object if std_plink_id is not None
                    - (bool): True or False indicating success or failure

                OR

                Tuple[list, bool]:
                    A tuple containing:
                    - payment_links (list): payment link details or error object
                    - (bool): True or False indicating success or failure
        """
        if std_plink_id:
            payment_link = self.client.payment_link.fetch(std_plink_id)
            return payment_link, 'error' not in payment_link

        options = {'count': limit, 'skip': offset}
        payment_links = self.client.payment_link.all(options)
        return payment_links, 'error' not in payment_links

    def get_upi_plinks(self, upi_plink_id=None):
        """
            Fetch UPI Payment Link by ID
                - client.payment_link.fetch(paymentLinkId)

            Fetch All UPI Payment Links
                - client.payment_link.all()

            Args:
                upi_plink_id (str or None): The payment link id.

            Returns:
                Tuple[dict, bool]:
                    A tuple containing:
                    - payment_link (dict): payment link details or error object if upi_plink_id is not None
                    - (bool): True or False indicating success or failure

                OR

                Tuple[list, bool]:
                    A tuple containing:
                    - payment_links (list): payment link details or error object
                    - (bool): True or False indicating success or failure
        """
        if upi_plink_id:
            payment_link = self.client.payment_link.fetch(upi_plink_id)
            return payment_link, 'error' not in payment_link

        payment_links = self.client.payment_link.all()
        return payment_links, 'error' not in payment_links

    def notify_by(self, plink_id, medium):
        """
            Send or Resend Notifications
            Use this endpoint to send or resend notifications to your customers via email and SMS.

            client.payment_link.notifyBy(paymentLinkId, medium)

            Args:
                plink_id (str): The payment link id.
                medium (str): Medium of notifying the customers. Possible values are: sms, email

            Returns:
                Dictionary[dict]:
                    {
                        "success": true
                    }
        """
        raise NotImplementedError('Not Implemented yet.')

    def update_standard_plink(self, std_plink_id):
        """
            Update Standard Payment Link

            Use this endpoint to edit the Standard Payment Link details such as the reference id,
            expiry date, enabling reminders and so on.

            client.payment_link.edit(paymentLinkId, {
                    "reference_id": "TS35",
                    "expire_by": 1653347540,
                    "reminder_enable":false,
                    "notes":{
                    "policy_name": "Jeevan Saral"
                }
            })

            Args:
                std_plink_id (str): The standard payment link id.

            Returns:
                Tuple[dict, bool]:
                    A tuple containing:
                    - payment_link (dict): payment link details or error object
                    - (bool): True or False indicating success or failure
        """
        raise NotImplementedError('Not Implemented yet.')

    def update_upi_plink(self, upi_plink_id):
        """
            Update Standard Payment Link

            Use this endpoint to edit the Standard Payment Link details such as the reference id,
            expiry date, enabling reminders and so on.

            client.payment_link.edit(paymentLinkId, {
                    "reference_id": "TS35",
                    "expire_by": 1653347540,
                    "reminder_enable":false,
                    "notes":{
                    "policy_name": "Jeevan Saral"
                }
            })

            Args:
                upi_plink_id (str): The UPI payment link id.

            Returns:
                Tuple[dict, bool]:
                    A tuple containing:
                    - payment_link (dict): updated payment link details or error object
                    - (bool): True or False indicating success or failure
        """
        raise NotImplementedError('Not Implemented yet.')

    def cancel_standard_plink(self, std_plink_id):
        """
            Cancel Standard Payment Link

            client.payment_link.cancel(paymentLinkId)

            Args:
                std_plink_id (str): The standard payment link id.

            Returns:
                Tuple[dict, bool]:
                    A tuple containing:
                    - payment_link (dict): cancelled payment link details or error object
                    - (bool): True or False indicating success or failure
        """
        raise NotImplementedError('Not Implemented yet.')

    def cancel_upi_plink(self, upi_plink_id):
        """
            Cancel UPI Payment Link

            client.payment_link.cancel(paymentLinkId)

            Args:
                upi_plink_id (str): The UPI payment link id.

            Returns:
                Tuple[dict, bool]:
                    A tuple containing:
                    - payment_link (dict): cancelled payment link details or error object
                    - (bool): True or False indicating success or failure
        """
        raise NotImplementedError('Not Implemented yet.')

    def validate_plink_signature(self, rzp_signature):
        pass