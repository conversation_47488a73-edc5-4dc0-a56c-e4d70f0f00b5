{% load static %}
{% load custom_template_tags %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tax Invoice - Caliper Business Solutions Private Limited</title>
    <link rel="stylesheet" type="text/css" href="{% static 'css/invoice.css' %}">
  </head>
  <body>
    <div class="invoice-container">
      <div class="invoice-sub-container">
        <!-- Header -->
        <div class="header">
          <div class="logo-container">
            <div class="logo">
              <!-- Placeholder for logo -->
              <!-- <div>Caliper</div> -->
              <svg
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                class="company-logo"
                viewBox="0 0 1999 692"
              >
                <path
                  d="M0 0 C8.08517995 4.27529596 15.14086519 9.73793132 18.75 18.3125 C18.8125 21.4375 18.8125 21.4375 17.75 24.3125 C11.55681197 29.52040812 5.48353292 29.69691538 -2.25 29.3125 C-5.25382357 29.01058898 -8.25222154 28.66914627 -11.25 28.3125 C-13.09472736 30.90876643 -14.92373122 33.51572409 -16.75 36.125 C-17.53697266 37.23198242 -17.53697266 37.23198242 -18.33984375 38.36132812 C-19.08427734 39.42963867 -19.08427734 39.42963867 -19.84375 40.51953125 C-20.30458984 41.1741333 -20.76542969 41.82873535 -21.24023438 42.50317383 C-22.49366637 44.40377067 -22.49366637 44.40377067 -22.25 47.3125 C-20.42289584 48.87162323 -20.42289584 48.87162323 -18.0625 50.25 C-15.46851242 51.86317635 -13.31971023 53.24041329 -11.1875 55.4453125 C-6.56719375 58.3821561 -1.80967395 58.04119312 3.5625 58.25 C31.52332595 59.90442882 59.20609227 67.26417818 82.5625 83.1875 C87.06212733 86.1760081 90.81801409 87.15495218 96.125 87.87109375 C103.89057111 89.17691151 110.4976276 93.75588174 116.75 98.3125 C117.34554688 98.72886719 117.94109375 99.14523438 118.5546875 99.57421875 C128.43220399 107.15305053 135.21010571 119.42849451 137.30859375 131.53515625 C138.033715 137.92668891 137.53762354 142.6119405 135.58129883 148.69555664 C134.78965858 151.18765409 134.10853434 153.6819924 133.4609375 156.21484375 C128.55877353 174.92231233 121.2403713 193.96289895 107.19921875 207.83203125 C104.76613203 210.29783174 103.10738299 212.11652133 101.75 215.3125 C102.15491888 219.49230779 103.12557032 223.04954454 104.49609375 226.9921875 C106.27168921 232.2890992 106.4451449 237.76957483 106.75 243.3125 C107.35134766 243.57764404 107.95269531 243.84278809 108.57226562 244.1159668 C123.59471251 250.77699696 138.12394254 258.05923411 152.47363281 266.06665039 C154.54383823 267.19966822 156.63156233 268.27305406 158.75 269.3125 C159.45125 268.69375 160.1525 268.075 160.875 267.4375 C166.06747835 263.59958122 171.36507854 262.46338264 177.75 263.3125 C188.84651587 265.79133188 196.36963512 272.3843084 202.625 281.6875 C211.04307819 295.82351809 213.29622429 312.88481461 209.99609375 328.9921875 C207.122777 337.12761766 202.74440158 344.20337055 194.8671875 348.2890625 C188.21445511 351.08060119 182.49789393 351.14374831 175.75 348.75 C170.529937 346.47623022 166.72056651 343.3432266 162.75 339.3125 C162.21632813 338.80074219 161.68265625 338.28898437 161.1328125 337.76171875 C154.52372779 330.86156036 150.99471449 321.91075163 149.8125 312.5625 C148.79583748 307.8406674 147.50697839 305.52982375 143.40527344 302.8671875 C140.73583394 301.33936839 138.01516192 299.94217084 135.2578125 298.58007812 C132.41008589 297.14068983 129.62971474 295.60237198 126.84375 294.046875 C116.98783473 288.57213286 107.24783644 283.46477648 96.75 279.3125 C95.85220825 280.47168945 95.85220825 280.47168945 94.9362793 281.65429688 C88.89607543 289.39220144 83.00723194 295.72785375 73.75 299.3125 C73.76571045 300.45388428 73.7814209 301.59526855 73.79760742 302.77124023 C74.19350648 334.3654066 74.00314008 365.81345656 63.5 396.0625 C61.74872933 401.33021098 61.41969299 406.19009756 61.25 411.6875 C60.69294944 429.17298722 55.04259251 444.55443927 42.75 457.3125 C31.1759065 467.09556589 18.72901112 472.93050208 3.4375 472.75 C2.52105713 472.74170166 1.60461426 472.73340332 0.66040039 472.72485352 C-15.53497774 472.32371634 -28.52654433 465.58282259 -39.8984375 454.23046875 C-42.00178464 451.62050145 -43.31376392 449.28683302 -44.8515625 446.33984375 C-48.39925696 440.28561381 -52.92930166 438.29865859 -59.25 435.6875 C-61.32072849 434.76556803 -63.38974889 433.83978988 -65.45703125 432.91015625 C-66.45428223 432.46945801 -67.4515332 432.02875977 -68.47900391 431.57470703 C-89.95325982 421.79304156 -111.53166376 406.03083624 -128.25 389.3125 C-133.30688768 389.88128877 -138.16997046 391.82517056 -142.99609375 393.375 C-149.16996716 395.15378706 -154.93784396 395.46887649 -161.3125 395.4375 C-162.10205078 395.44136719 -162.89160156 395.44523437 -163.70507812 395.44921875 C-167.68590915 395.4425451 -171.36455726 395.29754182 -175.25 394.3125 C-175.42631958 394.85668579 -175.60263916 395.40087158 -175.78430176 395.96154785 C-177.60473911 401.51009846 -179.59944659 406.9656628 -181.71875 412.40625 C-182.04033081 413.23781616 -182.36191162 414.06938232 -182.6932373 414.92614746 C-184.05233514 418.44040736 -185.41627231 421.9527833 -186.77978516 425.46533203 C-187.78196794 428.04915192 -188.78165527 430.63392801 -189.78125 433.21875 C-190.08446167 433.99714233 -190.38767334 434.77553467 -190.70007324 435.57751465 C-192.70917326 440.77375126 -194.5328476 446.01274861 -196.25 451.3125 C-195.78432617 451.70574463 -195.31865234 452.09898926 -194.83886719 452.50415039 C-186.55150136 459.6973977 -181.68736962 470.59484082 -180.3984375 481.37890625 C-179.72901949 494.60872003 -183.0791191 505.85536289 -191.25 516.3125 C-191.72824219 516.92867187 -192.20648438 517.54484375 -192.69921875 518.1796875 C-200.64262681 527.18355055 -213.01377706 531.50545139 -224.6640625 532.5234375 C-237.60893685 533.05767041 -249.7491407 528.41622125 -259.5 520.0625 C-269.42078393 510.63931025 -274.90413908 498.9796286 -275.5 485.25 C-275.27577019 473.10082141 -271.25172439 462.14363736 -263.21484375 452.99609375 C-255.05033035 445.18720343 -244.14286016 439.59438786 -232.75 439.1875 C-231.5125 439.249375 -231.5125 439.249375 -230.25 439.3125 C-230.07957184 438.64011795 -229.90914368 437.9677359 -229.73355103 437.27497864 C-227.98488129 430.59696247 -225.82618603 424.23330968 -223.2734375 417.82421875 C-222.90272125 416.87738724 -222.532005 415.93055573 -222.15005493 414.95503235 C-220.58307862 410.95325626 -219.00297872 406.95670103 -217.42382812 402.9597168 C-216.26481268 400.02054688 -215.11270833 397.07873351 -213.9609375 394.13671875 C-213.4250148 392.79126228 -213.4250148 392.79126228 -212.87826538 391.41862488 C-210.35042866 385.0454571 -210.35042866 385.0454571 -209.25 378.3125 C-211.27007673 375.63038213 -211.27007673 375.63038213 -214.1875 373.25 C-222.7620771 365.35875454 -228.80041398 355.50095992 -232.25 344.3125 C-232.25 343.6525 -232.25 342.9925 -232.25 342.3125 C-233.2709375 342.33570313 -234.291875 342.35890625 -235.34375 342.3828125 C-259.79990356 342.76100044 -285.18450889 340.55296807 -308.8125 334.0625 C-316.90978867 331.85350194 -324.91914562 332.06763309 -333.26000977 332.45996094 C-345.54418593 332.87366906 -355.16639121 328.50611291 -364.25 320.3125 C-375.32127833 307.7417721 -377.01935195 293.88444146 -376.42480469 277.80517578 C-375.42429378 263.53811385 -368.0860573 251.18903295 -357.87890625 241.51171875 C-348.65177435 233.79273619 -348.65177435 233.79273619 -343.20068359 233.02319336 C-342.55695801 232.78866455 -341.91323242 232.55413574 -341.25 232.3125 C-340.28099822 230.33792877 -340.28099822 230.33792877 -339.8125 227.8828125 C-338.3140084 222.21311646 -336.00622163 217.34618279 -333.25 212.1875 C-332.76724609 211.27806641 -332.28449219 210.36863281 -331.78710938 209.43164062 C-324.43269789 195.83885674 -315.27965869 181.1242509 -303.55712891 170.82519531 C-301.61450407 169.16431102 -301.61450407 169.16431102 -299.25 166.3125 C-299.2533126 163.35515258 -299.2533126 163.35515258 -300.25 160.3125 C-300.36630494 157.65371503 -300.43498564 155.03267711 -300.4375 152.375 C-300.44974609 151.69759766 -300.46199219 151.02019531 -300.47460938 150.32226562 C-300.48826957 145.57307133 -299.72024589 141.76972766 -298.25 137.3125 C-299.41273438 137.19132813 -300.57546875 137.07015625 -301.7734375 136.9453125 C-302.53865723 136.86458496 -303.30387695 136.78385742 -304.09228516 136.70068359 C-305.79711561 136.52352847 -307.50254807 136.35208377 -309.20849609 136.18603516 C-313.57581753 135.75083145 -317.88313467 135.23384614 -322.203125 134.453125 C-330.2616412 132.81058316 -330.2616412 132.81058316 -337.953125 134.7578125 C-340.15952089 136.77999061 -340.15952089 136.77999061 -342.09375 139.06640625 C-347.81843552 145.02962033 -356.91313212 148.43023523 -365.0625 148.625 C-370.29012455 148.56186444 -373.41428149 148.14821851 -377.25 144.3125 C-381.00666497 138.37454569 -380.60235684 133.07428421 -379.25 126.3125 C-376.55515615 116.56736195 -370.58264651 109.1086114 -363.25 102.3125 C-362.68925781 101.75820312 -362.12851562 101.20390625 -361.55078125 100.6328125 C-354.30289949 93.78187355 -345.84501831 89.80101987 -335.875 89.0625 C-330.86968563 89.40769409 -326.86821382 90.58640525 -323.375 94.3125 C-321.45530088 97.72529844 -321.11356269 100.46956514 -321.125 104.3125 C-321.12113281 105.611875 -321.12113281 105.611875 -321.1171875 106.9375 C-321.25 109.3125 -321.25 109.3125 -322.25 112.3125 C-321.57839844 112.34988281 -320.90679687 112.38726562 -320.21484375 112.42578125 C-314.54818195 112.79868416 -309.01363166 113.32146991 -303.40625 114.22265625 C-302.38281494 114.38016357 -301.35937988 114.5376709 -300.30493164 114.69995117 C-298.24059993 115.02280108 -296.1795942 115.3677556 -294.12280273 115.7355957 C-291.43007778 116.14681782 -288.97010985 116.46551724 -286.25 116.3125 C-283.80555443 114.28250642 -283.80555443 114.28250642 -282.25 111.3125 C-280.19691527 109.45640043 -278.16670005 107.71002212 -276 106 C-275.39953857 105.52578613 -274.79907715 105.05157227 -274.18041992 104.56298828 C-266.72652337 98.88483078 -258.15064349 94.27938116 -249.25 91.3125 C-248.46459967 89.56774476 -248.46459967 89.56774476 -248.07421875 87.40234375 C-247.88947998 86.58523926 -247.70474121 85.76813477 -247.5144043 84.92626953 C-247.32402588 84.04310059 -247.13364746 83.15993164 -246.9375 82.25 C-243.8875539 69.22590582 -239.90793079 57.63765611 -232.93920898 46.19580078 C-230.42188314 41.95383371 -229.36021419 38.24295621 -228.64453125 33.4140625 C-227.33685508 26.44842113 -220.70366341 21.28962572 -215.25 17.3125 C-198.85147449 8.08703369 -179.03384096 4.51391282 -160.453125 8.03125 C-158.12131272 8.69748208 -155.79856471 9.39601466 -153.484375 10.12109375 C-149.10600184 11.445216 -144.65943623 12.18838503 -140.15844727 12.97509766 C-119.43343711 16.61350513 -97.61416707 22.25220321 -80.12109375 34.50390625 C-74.99137865 37.44640058 -69.12801287 36.95124565 -63.375 37 C-61.10671265 37.04257877 -58.83847835 37.08809642 -56.5703125 37.13671875 C-55.57273926 37.14791748 -54.57516602 37.15911621 -53.54736328 37.1706543 C-49.43441333 37.34758484 -45.34157462 37.8578806 -41.25 38.3125 C-37.62 32.7025 -33.99 27.0925 -30.25 21.3125 C-33.66531165 18.57053838 -33.66531165 18.57053838 -37.12890625 15.890625 C-41.56730348 12.38933716 -44.58740447 8.79250238 -46.25 3.3125 C-46.25 0 -46.25 0 -45.25 -2.6875 C-34.42882562 -13.14796857 -11.44123058 -5.22570201 0 0 Z M-143.578125 33.81640625 C-143.88234375 34.49574219 -144.1865625 35.17507812 -144.5 35.875 C-149.55946276 44.96597117 -159.81667401 49.05262912 -169.25 52.3125 C-170.16523438 52.63347656 -171.08046875 52.95445313 -172.0234375 53.28515625 C-184.25191993 56.80945078 -199.85838708 57.11295962 -212.25 54.3125 C-218.42581 63.48798914 -225.25 75.96667992 -225.25 87.3125 C-223.50976562 87.55033203 -223.50976562 87.55033203 -221.734375 87.79296875 C-213.19169776 89.04056969 -205.51792972 90.51988347 -197.6328125 94.2265625 C-193.59446967 96.12646111 -193.59446967 96.12646111 -189.29663086 95.74267578 C-188.26614624 95.25516846 -188.26614624 95.25516846 -187.21484375 94.7578125 C-186.0222998 94.21314331 -186.0222998 94.21314331 -184.80566406 93.6574707 C-183.94166992 93.25488037 -183.07767578 92.85229004 -182.1875 92.4375 C-163.24961531 84.00198704 -143.65622789 78.05280429 -123.453125 73.50390625 C-122.55416504 73.29523926 -121.65520508 73.08657227 -120.72900391 72.87158203 C-119.18094281 72.52245263 -117.62610137 72.20087552 -116.06396484 71.92138672 C-113.94732463 71.544922 -113.94732463 71.544922 -111.25 70.3125 C-109.86458426 67.59947927 -109.86458426 67.59947927 -109.375 64.3125 C-107.70440709 57.65230437 -104.9204317 54.0102964 -100.078125 49.23828125 C-98.02050707 47.51329961 -98.02050707 47.51329961 -98.25 45.3125 C-108.05895474 41.16802353 -117.85741416 37.18090836 -128.125 34.3125 C-128.85444824 34.10536377 -129.58389648 33.89822754 -130.33544922 33.68481445 C-137.40019829 31.39678703 -137.40019829 31.39678703 -143.578125 33.81640625 Z M4.75 84.3125 C4.61722656 85.10914063 4.48445313 85.90578125 4.34765625 86.7265625 C2.75 96.3125 2.75 96.3125 1.80078125 99.17578125 C1.71895983 104.22982766 4.79801527 107.06313608 7.875 110.875 C21.67188437 128.8097301 33.77239413 148.73437564 42.71606445 169.56225586 C43.71277283 171.67434672 43.71277283 171.67434672 46.875 172.4375 C48.15375 172.72625 49.4325 173.015 50.75 173.3125 C61.42427738 175.93079058 69.29533419 179.30213282 78.01782227 185.95947266 C81.48246055 188.69218541 81.48246055 188.69218541 85.75 189.3125 C93.00717578 181.71740694 97.32277629 171.69680132 101.75 162.3125 C98.73302868 159.86611427 95.72637837 158.32859421 92.1875 156.75 C78.42624948 149.93650279 69.9048266 138.71504915 64.4765625 124.6015625 C63.10972673 120.29529498 62.61232261 116.32564949 62.625 111.8125 C62.62242187 110.8946875 62.61984375 109.976875 62.6171875 109.03125 C62.74755965 106.36245546 63.15621394 103.91384847 63.75 101.3125 C45.79620419 91.67517636 25.54446306 83.19851091 4.75 84.3125 Z M-120.0625 101.25 C-120.71806885 101.40322723 -121.3736377 101.55645447 -122.04907227 101.71432495 C-151.6501194 107.50887342 -151.6501194 107.50887342 -177.25 121.3125 C-177.32643731 123.00049049 -177.39717248 124.6889466 -177.43798828 126.37817383 C-177.97739613 144.83484148 -186.06431181 159.11656634 -199.2265625 171.62890625 C-202.92282699 175.00360279 -206.91372539 177.81585704 -211.25 180.3125 C-210.66441537 185.33802764 -209.56816099 189.91488998 -207.9140625 194.703125 C-207.67883881 195.39779877 -207.44361511 196.09247253 -207.20126343 196.80819702 C-206.43400457 199.06252654 -205.65584954 201.31284431 -204.875 203.5625 C-204.60977539 204.33051941 -204.34455078 205.09853882 -204.07128906 205.88983154 C-200.34450268 216.65551302 -196.45798897 227.35021711 -192.25 237.9375 C-191.58411865 239.65114746 -191.58411865 239.65114746 -190.90478516 241.39941406 C-190.48245605 242.43549805 -190.06012695 243.47158203 -189.625 244.5390625 C-189.26148438 245.44221191 -188.89796875 246.34536133 -188.5234375 247.27587891 C-187.43170265 249.45122014 -187.43170265 249.45122014 -185.31835938 250.14575195 C-182.02660532 250.41112825 -178.94404366 249.58085352 -175.75 248.875 C-156.61944623 245.03766645 -138.31955379 248.47572065 -121.625 258.5625 C-110.21688474 266.20069211 -101.08790333 275.75492668 -95.25 288.3125 C-90.60574889 287.69057728 -86.5529576 286.45263969 -82.17578125 284.79296875 C-81.50229645 284.53823792 -80.82881165 284.28350708 -80.13491821 284.02105713 C-77.94134243 283.1877613 -75.75169036 282.34474436 -73.5625 281.5 C-72.81300079 281.21271027 -72.06350159 280.92542053 -71.29129028 280.62942505 C-57.63983367 275.38802524 -44.26852662 269.65246259 -30.97607422 263.55810547 C-27.08114983 261.7784257 -23.16747231 260.041868 -19.25 258.3125 C-19.40388184 257.63783691 -19.55776367 256.96317383 -19.71630859 256.26806641 C-24.04853948 236.33504799 -22.38503659 216.13137535 -12.23046875 198.35546875 C-6.92895323 190.20974766 -0.63624529 184.18960719 7.75 179.3125 C6.14743314 173.65746521 3.46625096 168.79205528 0.625 163.6875 C0.1307251 162.79595215 -0.3635498 161.9044043 -0.87280273 160.98583984 C-4.68527413 154.17913536 -8.73605428 147.68054243 -13.25 141.3125 C-14.08486718 140.11604711 -14.91948934 138.91942321 -15.75390625 137.72265625 C-16.62654083 136.48102283 -17.50022441 135.24012593 -18.375 134 C-18.77082275 133.43684082 -19.16664551 132.87368164 -19.57446289 132.29345703 C-20.36413512 131.18788333 -21.16850498 130.09266337 -21.98706055 129.00830078 C-22.87374525 127.81771274 -23.71579853 126.59320627 -24.52270508 125.34716797 C-26.57159737 122.47052464 -28.00578842 120.58506803 -31.546875 119.80932617 C-34.29028454 119.72944883 -36.95067253 119.85919014 -39.6875 120.0625 C-57.95531585 120.72982435 -78.54905173 116.68209037 -92.6875 104.15234375 C-94.15101146 102.75063598 -95.5876217 101.31980317 -96.984375 99.8515625 C-103.22559698 95.61183585 -113.30554734 99.65975068 -120.0625 101.25 Z M-282.25 185.3125 C-295.40118765 197.07120895 -308.16370486 214.27078948 -313.25 231.3125 C-312.45335937 231.68761719 -311.65671875 232.06273437 -310.8359375 232.44921875 C-298.14659972 238.57776753 -290.78147824 245.76579321 -285.875 259.1875 C-280.64185626 276.69146354 -284.26448186 292.38789115 -292.25 308.3125 C-288.00098241 308.96490464 -283.75062915 309.60818609 -279.5 310.25 C-278.31277344 310.43240234 -277.12554687 310.61480469 -275.90234375 310.80273438 C-265.09189424 312.42725548 -254.43612877 313.49589258 -243.5 313.375 C-242.2934375 313.36823242 -242.2934375 313.36823242 -241.0625 313.36132812 C-239.12496596 313.34979518 -237.18747149 313.33187471 -235.25 313.3125 C-234.90388672 311.77142578 -234.90388672 311.77142578 -234.55078125 310.19921875 C-232.5935469 301.7682124 -230.33978057 294.00176581 -226.25 286.3125 C-225.80914062 285.47332031 -225.36828125 284.63414062 -224.9140625 283.76953125 C-221.20468241 277.16027667 -216.47678427 271.7715858 -211.25 266.3125 C-213.60349357 258.85977037 -216.48338636 251.65378101 -219.40567017 244.41061401 C-226.43360668 226.94657154 -232.50310993 209.23420548 -238.25 191.3125 C-238.96953857 191.33344727 -239.68907715 191.35439453 -240.43041992 191.37597656 C-266.57607029 192.03730895 -266.57607029 192.03730895 -277.25 187.3125 C-278.91603023 186.64424444 -280.58250876 185.97710153 -282.25 185.3125 Z M-5.25 282.3125 C-7.10600746 282.97113747 -7.10600746 282.97113747 -9.17578125 284.09375 C-10.43660522 284.7273645 -10.43660522 284.7273645 -11.72290039 285.3737793 C-12.65996826 285.84855713 -13.59703613 286.32333496 -14.5625 286.8125 C-25.1317254 292.01598267 -35.89136 296.59542897 -46.8125 301 C-47.60914063 301.3213089 -48.40578125 301.6426178 -49.2265625 301.97366333 C-61.80269974 307.03079779 -74.44739369 311.85508534 -87.25 316.3125 C-87.34796875 317.52164063 -87.4459375 318.73078125 -87.546875 319.9765625 C-89.01147719 336.61635518 -93.03267419 349.83435452 -101.25 364.3125 C-101.9285899 365.63980478 -102.60210115 366.96994672 -103.25 368.3125 C-88.44383341 381.39342631 -72.14724911 393.73949672 -54.25 402.3125 C-53.97027344 401.29671875 -53.69054687 400.2809375 -53.40234375 399.234375 C-48.65832351 382.67574497 -41.15915687 368.8569045 -25.98046875 359.8125 C-11.79090209 352.01737325 3.38355671 349.79672883 19.1875 353.9375 C24.54877645 355.65459084 29.11075256 357.98265804 33.76171875 361.140625 C35.79609395 362.5663798 35.79609395 362.5663798 38.75 362.3125 C41.61888286 343.52996461 42.11823724 325.25500111 41.75 306.3125 C40.7084375 306.1165625 39.666875 305.920625 38.59375 305.71875 C22.89509154 302.54060729 10.39845415 296.93391812 -0.9921875 285.53515625 C-3.0432853 283.28720137 -3.0432853 283.28720137 -5.25 282.3125 Z "
                  fill="#5E55FC"
                  transform="translate(442.25,47.6875)"
                />
                <path
                  d="M0 0 C0.74894531 0.65355469 1.49789063 1.30710938 2.26953125 1.98046875 C2.88441406 2.49996094 3.49929688 3.01945312 4.1328125 3.5546875 C10.11772916 9.01678385 14.16844293 16.07080557 18.26953125 22.98046875 C18.76710938 23.81578125 19.2646875 24.65109375 19.77734375 25.51171875 C38.1708316 59.29095429 35.74869141 109.44595411 25.4921875 145.32421875 C18.15988329 167.25085369 6.10797749 186.013416 -14.98754883 196.82299805 C-36.0204173 207.04116196 -60.65233658 208.87734597 -82.953125 201.984375 C-96.39431266 196.86645243 -107.74534408 187.95815576 -115.73046875 175.98046875 C-115.73633499 177.05629211 -115.74220123 178.13211548 -115.74824524 179.24053955 C-115.80566552 189.42051711 -115.87852673 199.600285 -115.96618366 209.78004837 C-116.01075249 215.01252935 -116.05003783 220.24493487 -116.0769043 225.47753906 C-116.10307246 230.53515938 -116.14356888 235.59248724 -116.19365501 240.64992523 C-116.21023285 242.57140186 -116.2218033 244.49292897 -116.22808266 246.41446686 C-116.32013138 272.01230737 -116.32013138 272.01230737 -122.35546875 279.98046875 C-131.53134172 288.70427519 -142.44668313 287.46199936 -154.29296875 287.23046875 C-156.08266234 287.21037622 -157.87237767 287.19213031 -159.66210938 287.17578125 C-164.01877153 287.13199596 -168.37437769 287.06312099 -172.73046875 286.98046875 C-172.80073115 258.77184791 -172.85354106 230.56324031 -172.88590527 202.35454941 C-172.90133583 189.25505386 -172.92235207 176.15561286 -172.95678711 163.05615234 C-172.98680658 151.63091977 -173.0060714 140.20572674 -173.01273423 128.78045583 C-173.01662533 122.73791305 -173.02568951 116.69548333 -173.04765892 110.65297699 C-173.06822282 104.95012261 -173.07426287 99.24742644 -173.06979179 93.5445385 C-173.07060818 91.46628137 -173.07645398 89.38801821 -173.08808517 87.30979347 C-173.25501309 55.90803068 -167.58086489 26.34396224 -145.578125 2.6640625 C-110.11329392 -31.08366317 -36.74144927 -30.83967317 0 0 Z M-104 39.71484375 C-110.29849664 47.96118246 -113.90257321 57.84267889 -115.73046875 67.98046875 C-115.91351562 68.97433594 -116.0965625 69.96820313 -116.28515625 70.9921875 C-119.34580163 93.43997481 -118.0249257 121.03389028 -104.3125 139.98828125 C-97.99564305 147.94284185 -90.8226786 152.80470018 -80.73046875 154.98046875 C-66.59261064 156.16338244 -54.03333535 155.48574202 -42.79296875 146.01171875 C-35.5909104 138.9187825 -31.70793057 130.53452208 -28.73046875 120.98046875 C-28.40949219 119.954375 -28.08851563 118.92828125 -27.7578125 117.87109375 C-21.91849736 94.73111003 -23.71590367 66.60814374 -35.0703125 45.48828125 C-41.00273813 35.72689393 -49.79910075 30.49410798 -60.62329102 27.44775391 C-76.34110359 23.96219102 -92.86350561 27.99221808 -104 39.71484375 Z "
                  fill="#146BE1"
                  transform="translate(1591.73046875,246.01953125)"
                />
                <path
                  d="M0 0 C20.82075965 18.76470562 30.23621306 45.92297989 32.48071289 73.19946289 C26.80650653 73.27406813 21.13259081 73.32827897 15.45800781 73.36425781 C13.53087383 73.37926941 11.60377357 73.39968505 9.67675781 73.42578125 C6.8938765 73.46250232 4.11145001 73.47926581 1.32836914 73.49243164 C0.05280907 73.51565742 0.05280907 73.51565742 -1.2485199 73.53935242 C-9.53828857 73.54164268 -17.02584749 71.53851325 -23.51928711 66.19946289 C-27.33470019 61.96353385 -30.08345213 57.23302962 -32.83178711 52.26196289 C-39.90964724 40.08252826 -50.20084955 32.49422224 -63.6965332 28.56567383 C-84.59763081 23.47744428 -107.07968199 25.67412018 -125.85522461 36.44165039 C-143.64668568 48.18088424 -153.12441684 66.64091015 -157.55444336 86.96508789 C-163.82722598 118.94965095 -162.34042514 156.41457338 -144.27563477 184.53173828 C-134.69731468 198.65118251 -120.76291179 207.26296095 -104.10522461 210.74633789 C-103.25186523 210.89586914 -102.39850586 211.04540039 -101.51928711 211.19946289 C-99.60116211 211.53977539 -99.60116211 211.53977539 -97.64428711 211.88696289 C-78.15002082 213.77221087 -59.17799841 210.64175121 -43.45678711 198.32446289 C-33.34232678 188.4997397 -28.48111004 175.40553931 -24.51928711 162.19946289 C-5.70928711 162.19946289 13.10071289 162.19946289 32.48071289 162.19946289 C30.1284855 182.19339574 30.1284855 182.19339574 28.35571289 188.26196289 C28.14543457 188.99745361 27.93515625 189.73294434 27.71850586 190.49072266 C25.2757804 198.54826404 21.610873 205.88848744 17.48071289 213.19946289 C16.92512695 214.1959082 16.36954102 215.19235352 15.79711914 216.21899414 C2.17416205 239.05459847 -21.57431183 253.76122256 -46.90209961 260.38696289 C-85.520296 269.838969 -129.02777252 267.38167648 -163.68334961 246.69555664 C-169.83210531 242.82819065 -175.19275971 238.11103419 -180.51928711 233.19946289 C-181.5131543 232.32741211 -181.5131543 232.32741211 -182.52709961 231.43774414 C-208.17330363 207.75973718 -217.40364984 168.5400708 -219.51928711 135.19946289 C-221.22713792 89.08423177 -214.54789744 44.28694086 -183.31225586 8.68383789 C-140.31484606 -36.94606638 -47.23357043 -40.94978734 0 0 Z "
                  fill="#146BE1"
                  transform="translate(967.519287109375,186.800537109375)"
                />
                <path
                  d="M0 0 C23.09222012 19.93717842 30.385785 48.54534998 33.3125 77.8125 C33.3125 88.7025 33.3125 99.5925 33.3125 110.8125 C-13.2175 110.8125 -59.7475 110.8125 -107.6875 110.8125 C-103.59443037 135.24784558 -103.59443037 135.24784558 -88.4375 153.1875 C-78.20430494 159.3894364 -66.0848834 160.51426165 -54.44921875 158.15625 C-44.06943115 155.03288979 -35.62069236 149.15619327 -27.953125 141.609375 C-21.43921489 136.44317043 -15.29521635 136.39977816 -7.28515625 136.51953125 C-6.20745468 136.52377609 -5.12975311 136.52802094 -4.01939392 136.53239441 C-0.59600254 136.54911281 2.8267882 136.58675317 6.25 136.625 C8.57876751 136.64005564 10.90754429 136.6537425 13.23632812 136.66601562 C18.92858844 136.69899392 24.62050129 136.74912517 30.3125 136.8125 C25.08979184 154.29698702 25.08979184 154.29698702 21.4375 161.1875 C20.92497681 162.1787085 20.92497681 162.1787085 20.40209961 163.18994141 C9.43419597 183.96775747 -6.95141491 197.93453922 -29.375 205.125 C-39.04629876 207.86481103 -48.66014008 209.25391066 -58.6875 209.8125 C-59.8321875 209.87630859 -59.8321875 209.87630859 -61 209.94140625 C-89.19399828 210.67562496 -116.79978697 202.53419211 -137.75 183.25 C-161.45642491 158.18615198 -166.54093678 121.75532036 -165.875 88.59375 C-164.83804614 57.32959122 -158.40279033 25.6580291 -135.1484375 3.16357422 C-101.42394392 -28.02481941 -35.46051155 -27.5030335 0 0 Z M-95.6875 39.8125 C-101.92471215 47.75210417 -106.6875 59.65190776 -106.6875 69.8125 C-80.2875 69.8125 -53.8875 69.8125 -26.6875 69.8125 C-27.9946998 55.43330219 -31.37879322 44.55113575 -41.9375 34.5625 C-58.04235414 21.45721958 -82.12711707 25.23720276 -95.6875 39.8125 Z "
                  fill="#146BE1"
                  transform="translate(1806.6875,243.1875)"
                />
                <path
                  d="M0 0 C9.94859316 9.30004563 16.11065072 22.00081771 16.78533173 35.74262238 C16.83123093 38.08699935 16.8454714 40.42958712 16.84863281 42.77441406 C16.85359772 43.66321259 16.85856262 44.55201111 16.86367798 45.46774292 C16.87824588 48.4003783 16.88509615 51.33295941 16.890625 54.265625 C16.89486607 55.77926258 16.89486607 55.77926258 16.89919281 57.3234787 C16.91417141 62.67398844 16.92340657 68.02447137 16.92773438 73.375 C16.93318658 78.85486833 16.95714919 84.3344841 16.98570633 89.81427383 C17.00450821 94.06643338 17.00954776 98.31853455 17.01107025 102.57073212 C17.01406291 104.5877799 17.02197401 106.60482752 17.03525543 108.6218338 C17.20523149 136.44387426 12.90259871 161.186965 -7.3125 181.75 C-7.92738281 182.37777344 -8.54226563 183.00554687 -9.17578125 183.65234375 C-33.12009014 206.26356478 -69.99739737 208.73978467 -101.03222656 207.89794922 C-117.26104126 207.16598571 -132.57963534 201.92232515 -145.3125 191.75 C-145.94542969 191.27949219 -146.57835937 190.80898438 -147.23046875 190.32421875 C-160.61132141 179.83812009 -166.31824091 163.02457867 -168.3125 146.75 C-169.44331816 127.80879581 -165.87119566 108.58287626 -153.53515625 93.58984375 C-134.19102575 73.05895677 -104.503521 68.95089176 -77.97338867 65.62451172 C-58.74895394 63.25479998 -58.74895394 63.25479998 -42.3125 53.75 C-39.62804897 50.19705011 -39.09436077 47.59097072 -39.3125 43.25 C-40.03914135 38.74633747 -41.70304138 35.58724272 -45.3125 32.75 C-57.57821588 25.00726685 -74.79352037 25.47289795 -88.55078125 28.0625 C-95.69324315 29.84053861 -100.09151632 32.73829597 -104.3125 38.75 C-104.6425 39.74 -104.9725 40.73 -105.3125 41.75 C-123.7925 41.75 -142.2725 41.75 -161.3125 41.75 C-160.63025889 34.92758887 -160.08994054 29.88313494 -157.6875 23.75 C-157.42461182 23.06502441 -157.16172363 22.38004883 -156.89086914 21.67431641 C-154.50422193 15.73045168 -151.33202148 10.73842761 -147.3125 5.75 C-146.57 4.71875 -145.8275 3.6875 -145.0625 2.625 C-113.35709291 -30.52156196 -33.86328207 -30.06611257 0 0 Z M-44.53515625 95.5078125 C-57.19548993 99.89132132 -69.67069705 103.89056076 -82.82446289 106.54541016 C-94.21368752 108.99522229 -103.54217368 113.91425352 -110.0625 123.75 C-111.86195774 126.76789934 -113.03908307 129.19557996 -113.3125 132.75 C-113.395 133.801875 -113.4775 134.85375 -113.5625 135.9375 C-112.96705392 145.01805269 -110.18069952 151.65795096 -103.3125 157.75 C-94.0216591 163.51032136 -81.44054589 162.93123242 -71.0625 160.625 C-60.10045004 157.4572033 -50.7532949 151.78462283 -44.59375 142.02734375 C-36.19363829 126.40066535 -36.93497824 111.04049649 -37.3125 93.75 C-39.98804834 93.75 -42.00738622 94.61700389 -44.53515625 95.5078125 Z "
                  fill="#146BE1"
                  transform="translate(1191.3125,244.25)"
                />
                <path
                  d="M0 0 C39.91935484 0 39.91935484 0 48.9375 8.875 C54.76944223 15.72430807 55.43621812 22.16627966 55.36076355 30.82627869 C55.36332682 31.90623393 55.3658901 32.98618918 55.36853105 34.09887034 C55.37346615 37.71181816 55.35697998 41.32434663 55.34057617 44.93725586 C55.33972984 47.52938833 55.34020515 50.12152151 55.34190369 52.71365356 C55.34347443 58.2913591 55.33408616 63.86895911 55.31719017 69.44663811 C55.2927776 77.51094273 55.2849926 85.57519649 55.2812262 93.63953406 C55.27466525 106.72431789 55.25471378 119.80904551 55.22631836 132.89379883 C55.19876855 145.60212931 55.17753949 158.3104445 55.16479492 171.01879883 C55.16400675 171.8025396 55.16321858 172.58628037 55.16240653 173.39377087 C55.15849133 177.32568707 55.15470022 181.25760338 55.15097082 185.18951976 C55.11988601 217.79306298 55.06458893 250.39650166 55 283 C36.85 283 18.7 283 0 283 C0 189.61 0 96.22 0 0 Z "
                  fill="#146BE1"
                  transform="translate(1237,165)"
                />
                <path
                  d="M0 0 C0 19.47 0 38.94 0 59 C-14.355 59.495 -14.355 59.495 -29 60 C-42.54706838 63.69465501 -51.21713844 67.35451389 -58.25 79.5625 C-64.68026379 94.50796679 -63.47436435 111.25226195 -63.51171875 127.15234375 C-63.52697291 129.86619664 -63.54660002 132.58001335 -63.56611633 135.2938385 C-63.60948666 141.66818747 -63.63821324 148.04251146 -63.66143972 154.41696221 C-63.69132952 162.39980062 -63.74119255 170.38249976 -63.79101562 178.36523438 C-63.87962822 192.57678001 -63.93845892 206.78825419 -64 221 C-82.48 221 -100.96 221 -120 221 C-120.16121214 189.28599018 -120.16121214 189.28599018 -120.1953125 175.86328125 C-120.2189275 166.642649 -120.24648862 157.42216431 -120.30175781 148.20166016 C-120.34201375 141.48251581 -120.36752326 134.76349267 -120.37635398 128.04423106 C-120.38150429 124.49651146 -120.39333666 120.94913817 -120.42292023 117.40153122 C-120.69248365 83.72331396 -116.37132638 52.03173453 -91.8828125 26.8203125 C-66.90735332 3.30861649 -32.61984228 0 0 0 Z "
                  fill="#146BE1"
                  transform="translate(1984,227)"
                />
                <path
                  d="M0 0 C42 0 42 0 50 7 C55.46133581 13.66413001 56.30488753 22.21773367 56.24050903 30.5168457 C56.24221788 31.33630802 56.24392673 32.15577034 56.24568737 33.00006485 C56.24896197 35.72539475 56.23801226 38.45047432 56.22705078 41.17578125 C56.22648623 43.13752741 56.22680409 45.09927399 56.22793579 47.0610199 C56.22850858 52.37239748 56.21676614 57.68368435 56.20278788 62.99504089 C56.19026827 68.55105121 56.18910905 74.1070637 56.18673706 79.66308594 C56.18052591 90.17784022 56.16412001 100.6925528 56.14403808 111.20728874 C56.12166774 123.18063845 56.11068279 135.15399017 56.10064721 147.12735558 C56.07975348 171.75159881 56.04308177 196.3757849 56 221 C37.52 221 19.04 221 0 221 C0 148.07 0 75.14 0 0 Z "
                  fill="#146BE1"
                  transform="translate(1334,227)"
                />
                <path
                  d="M0 0 C7.24452812 5.48234561 12.62629547 12.19867148 15.45703125 20.859375 C15.64652344 21.43816406 15.83601563 22.01695312 16.03125 22.61328125 C18.18980553 34.00015676 17.0460788 45.70088377 10.76171875 55.50390625 C3.26882976 65.62082316 -6.1410998 71.57732244 -18.54296875 73.859375 C-33.13848135 75.38476256 -44.78794928 72.03938874 -56.73046875 63.609375 C-65.33351516 55.1975074 -69.88619293 44.89580317 -70.04296875 32.859375 C-69.8833536 21.61771941 -66.30427738 12.97347039 -58.54296875 4.859375 C-42.62804724 -10.07935244 -18.23923284 -11.60417333 0 0 Z "
                  fill="#146BE1"
                  transform="translate(366.54296875,192.140625)"
                />
                <path
                  d="M0 0 C7.71565923 5.40355933 13.20280073 13.43702037 15.5703125 22.5625 C16.87812054 34.58816923 15.74695295 44.70641803 8.8203125 54.8125 C3.01469721 62.01146296 -5.11390219 67.74808147 -14.47363281 68.79003906 C-26.74367261 69.63864121 -36.00294189 68.52531253 -45.6796875 60.4375 C-54.2278093 52.23882972 -58.19954238 43.53718665 -58.8046875 31.8125 C-58.5269825 21.30871687 -54.79663352 12.02002323 -47.4296875 4.5625 C-34.33401286 -7.81180273 -15.30888976 -9.05734957 0 0 Z "
                  fill="#146BE1"
                  transform="translate(389.4296875,542.4375)"
                />
                <path
                  d="M0 0 C7.05491356 5.45757464 11.69576637 13.82216545 12.9453125 22.6640625 C13.95250211 35.8896179 11.51329579 47.30421731 2.9453125 57.6640625 C-3.48880034 64.57764048 -10.55447484 67.84782566 -19.9921875 68.2265625 C-28.71525568 67.9351669 -34.88524671 65.27658218 -40.9296875 58.9765625 C-47.97099106 50.20814675 -49.84863471 39.63496939 -49.0546875 28.6640625 C-47.35236323 18.90585724 -43.08315291 8.28357623 -35.015625 2.1015625 C-24.18414889 -5.4333774 -11.49422179 -7.47342937 0 0 Z "
                  fill="#146BE1"
                  transform="translate(585.0546875,450.3359375)"
                />
                <path
                  d="M0 0 C7.93617219 6.40268872 12.02073467 16.33116178 13.12890625 26.27734375 C13.56448933 36.97020583 10.89825159 47.26390256 3.87890625 55.49609375 C-2.75284494 61.72668041 -9.74238886 64.8283888 -18.765625 64.63671875 C-27.98232093 63.87857118 -34.16133324 59.29449952 -40.1875 52.4375 C-46.59156827 42.41374097 -47.20414374 32.06156683 -46.1875 20.4375 C-45.31969506 16.75944592 -44.06008016 13.70801326 -42.1875 10.4375 C-41.61 9.385625 -41.0325 8.33375 -40.4375 7.25 C-29.71148037 -6.15752453 -14.66933418 -9.86338089 0 0 Z "
                  fill="#146BE1"
                  transform="translate(104.1875,422.5625)"
                />
                <path
                  d="M0 0 C8.91 -0.0928125 8.91 -0.0928125 18 -0.1875 C19.85882812 -0.21481201 21.71765625 -0.24212402 23.6328125 -0.27026367 C25.13020176 -0.27903744 26.62759825 -0.28667926 28.125 -0.29296875 C29.26195312 -0.3159906 29.26195312 -0.3159906 30.421875 -0.33947754 C37.09855175 -0.34270403 43.43957506 1.18856589 48.75 5.5 C59.06185887 16.60507878 56 29.48578537 56 44 C37.52 44 19.04 44 0 44 C0 29.48 0 14.96 0 0 Z "
                  fill="#146BE1"
                  transform="translate(1334,165)"
                />
                <path
                  d="M0 0 C4.74616766 4.63040747 6.77436423 9.09406374 7.0625 15.75 C6.51745235 28.86748004 0.11453858 39.83519074 -9.25 48.703125 C-15.8891735 54.15673181 -22.1204122 56.3269188 -30.73046875 55.91796875 C-34.54549894 55.32892369 -36.67729657 54.18942898 -39.4375 51.5 C-43.58604124 44.66240432 -43.40468728 37.22994212 -42.4375 29.5 C-39.48391005 17.7403363 -31.70735031 7.82472489 -21.4375 1.5 C-14.63772611 -2.34892862 -7.20995544 -3.14616237 0 0 Z "
                  fill="#146BE1"
                  transform="translate(58.4375,248.5)"
                />
                <path
                  d="M0 0 C0.928125 0.763125 1.85625 1.52625 2.8125 2.3125 C3.37582031 2.77269531 3.93914063 3.23289063 4.51953125 3.70703125 C12.7484338 11.07539497 16.87552721 21.52199164 18.02734375 32.328125 C18.32703087 38.41776724 17.06784264 44.26152259 13.40625 49.21484375 C10.16442139 52.46431818 7.47341441 53.718191 2.875 53.9375 C-5.16698082 53.37784627 -10.75089629 49.60948432 -16.2265625 43.86328125 C-24.28631235 33.37918434 -27.06000014 22.4000021 -26.1875 9.3125 C-25.00813603 3.61557237 -21.81597112 0.5240922 -17.1875 -2.6875 C-11.44407577 -4.74466851 -5.19177262 -2.74858551 0 0 Z "
                  fill="#146BE1"
                  transform="translate(631.1875,214.6875)"
                />
                <path
                  d="M0 0 C12.5635906 3.39884577 24.59334261 11.78095599 31.21875 23.078125 C32.49109815 26.20810144 32.42269985 28.67878686 32 32 C29.12272852 34.97008668 27.17219702 34.99453343 23.0625 35.125 C10.16163625 33.08576197 -1.65388772 24.25211752 -9.24609375 13.953125 C-11.61930949 9.95724282 -11.9242701 7.61952415 -12 3 C-8.87874597 -1.29908574 -4.84627301 -0.66119694 0 0 Z "
                  fill="#146BE1"
                  transform="translate(525,90)"
                />
                <path
                  d="M0 0 C1.03125 -0.03738281 2.0625 -0.07476562 3.125 -0.11328125 C6.69983103 0.41614345 7.83892264 1.47060591 10 4.3125 C10.29047168 8.57275127 8.70039708 11.04161376 6.125 14.25 C-1.64543815 22.56071401 -13.86324765 29.68288405 -25.4375 30.48046875 C-26.283125 30.48691406 -27.12875 30.49335937 -28 30.5 C-28.845625 30.51417969 -29.69125 30.52835938 -30.5625 30.54296875 C-33.15408616 30.29793096 -34.77024992 29.61834901 -37 28.3125 C-37.60820873 24.13106496 -37.81902264 21.64397123 -35.5703125 17.98828125 C-26.95272652 7.79599583 -13.46794373 0.31986366 0 0 Z "
                  fill="#146BE1"
                  transform="translate(180,71.6875)"
                />
                <path
                  d="M0 0 C0.98484375 -0.01160156 1.9696875 -0.02320312 2.984375 -0.03515625 C10.25357679 -0.00607944 16.34638753 0.70693663 22 5.6875 C23.375 8.375 23.375 8.375 23.1875 11.375 C19.95597135 16.72373708 14.3251864 19.02358863 8.4375 20.6875 C-1.8843858 22.57742275 -15.04915227 23.08245456 -24.625 18.375 C-27.47668247 16.33808395 -28.35005069 15.32482487 -29.3125 12 C-28.17515793 7.65742118 -25.29873854 5.73803182 -21.625 3.375 C-14.5365294 0.37334683 -7.61125825 -0.09037114 0 0 Z "
                  fill="#146BE1"
                  transform="translate(322.625,27.625)"
                />
              </svg>
            </div>
          </div>
          <div>
            <div class="company-name">
              Caliper Business Solutions Private Limited
            </div>
            <div class="address">
              Prospect Arcade, #2M-126, 2nd Main<br />
              1st Cross Kasturi Nagar<br />
              Bangalore Karnataka 560043<br />
              India<br />
              GSTIN: 29AAECC7210R1Z7<br />
              Contact: +91 8884788422, 8884766599<br />
              Email: <EMAIL><br />
              CIN: U93030KA2012PTC147210<br />
            </div>
          </div>
          <div class="invoice-taxdetails-class">
            <div class="logo1">
              <!-- Placeholder for logo -->
              <!-- <div>Caliper</div> -->
              <svg
                viewBox="0 0 1608 322"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="company-logo1"
              >
                <!-- Blue paths -->
                <path
                  d="M366.977 216.39V196.53H446.717C455.027 196.53 458.567 194.68 458.567 187.6V181.91C458.567 175.29 455.027 172.82 446.877 172.82H396.877C373.327 172.82 367.167 164.2 367.167 146.19V141C367.167 119.45 375.797 111.44 396.877 111.44H429.517V131.3H398.997C391.917 131.3 388.527 133.45 388.527 141.77V145.16C388.527 151.78 391.297 154.24 398.847 154.24H447.947C471.347 154.24 479.947 161.94 479.947 180.87V189.18C479.947 208.73 471.327 216.43 447.947 216.43L366.977 216.39Z"
                  fill="#597EF7"
                />
                <path
                  d="M570.776 216.39C539.836 216.39 530.776 206.84 530.776 180.39V147.39C530.776 120.92 539.856 111.39 570.776 111.39H643.596V131.56H571.856C557.546 131.56 553.996 135.72 553.996 148.65V179.43C553.996 192.52 557.546 196.37 571.996 196.37H643.576V216.37L570.776 216.39Z"
                  fill="#597EF7"
                />
                <path
                  d="M735.176 216.39C704.236 216.39 695.176 206.84 695.176 180.39V111.39H718.406V179.39C718.406 191.56 722.406 196.33 737.186 196.33H799.536V216.33L735.176 216.39Z"
                  fill="#597EF7"
                />
                <path
                  d="M887.876 216.39C856.876 216.39 847.876 206.84 847.876 180.39V147.39C847.876 120.92 856.956 111.39 887.876 111.39H913.736V131.25H887.876C874.326 131.25 871.096 135.25 871.096 147.41V154.5H960.536V173.12H871.096V180.51C871.096 193.13 874.176 196.51 887.876 196.51H961.146V216.37L887.876 216.39Z"
                  fill="#597EF7"
                />
                <path
                  d="M1111.23 213.77L1034.72 141.77V216.43H1013.02V118.83C1013.02 112.21 1018.1 109.13 1025.49 109.13C1030.72 109.13 1034.11 110.67 1036.42 112.98L1113.54 187.33V111.4H1135.71V209C1135.71 215.93 1130.94 218.7 1124.01 218.7C1121.65 218.786 1119.31 218.393 1117.11 217.546C1114.91 216.698 1112.91 215.414 1111.23 213.77Z"
                  fill="#597EF7"
                />

                <!-- Teal paths -->
                <path
                  d="M1375.68 192.22H1303.02L1288.09 216.39H1261.15L1322.15 119.39C1325.84 113.39 1330.31 109.07 1339.85 109.07C1349.24 109.07 1353.7 113.38 1357.4 119.39L1418.05 216.39H1390.96L1375.68 192.22ZM1313.95 174.22H1364.75L1339.35 132.81L1313.95 174.22Z"
                  fill="#50E3C2"
                />
                <path
                  d="M1465.41 216.39V111.39H1488.81V216.39H1465.41Z"
                  fill="#50E3C2"
                />

                <!-- Decorative elements -->
                <path
                  d="M194.566 133.31V0L0.40625 120.81L192.906 199.13L72.0963 122.8L170.556 45.25L194.566 133.31Z"
                  fill="#597EF7"
                />
                <path
                  d="M103.847 188.03V321.34L298.007 200.53L105.507 122.2L226.317 198.54L127.857 276.09L103.847 188.03Z"
                  fill="#50E3C2"
                />
                <path
                  d="M1577.52 158.114L1560.15 118.909H1567.18L1579.33 147.673L1591.47 118.909H1598.5L1581.14 158.114H1577.52ZM1558.87 158.114V118.909H1565.69V158.114H1558.87ZM1592.96 158.114V118.909H1599.78V158.114H1592.96ZM1533.3 158.114V122.318H1540.12V158.114H1533.3ZM1521.37 124.875V118.909H1552.05V124.875H1521.37Z"
                  fill="#597EF7"
                />
              </svg>
            </div>
            <div>
              <div class="invoice-title">TAX INVOICE</div>
              <div class="invoice-number">Invoice# {{ invoice_number }}</div>
              <div class="invoice-number">Order ID# {{ order_no }}</div>
            </div>
          </div>
        </div>

        <!-- Invoice Details -->
        <div class="invoice-details">
          <div class="left-details">
            <div class="detail-row">
              <div class="detail-label">Invoice Date</div>
              <div class="detail-value">: {{ created_at|format_timestamp:"%d/%m/%Y" }}</div>
            </div>
            <div class="detail-row">
              <div class="detail-label">Terms</div>
              <div class="detail-value">: </div>
            </div> 
            <div class="detail-row">
              <div class="detail-label">Due Date</div>
              <div class="detail-value">: </div>
            </div>
            
          </div>
          <div class="right-details">
            <div class="detail-row">
              <div class="detail-label">Place Of Supply</div>
              <div class="detail-value">: Karnataka (29)</div>
            </div>
          </div>
        </div>

        <!-- Bill To -->
        <div class="bill-to">
          <div class="bill-to-title">Bill To</div>
          <div class="bill-to-address">
            <b>{{ customer_details.customer_name }}</b><br/>
            {{ customer_details.billing_address }}<br/>
            GSTIN: {{ customer_details.gstin }} <br/>
            Contact: {{ customer_details.customer_contact }} <br/>
            Email: {{ customer_details.customer_email }}
          </div>
        </div>

        <!-- Invoice Table -->
        <table class="invoice-table">
          <thead>
            <tr>
              <th style="width: 5%">#</th>
              <th style="width: 40%">Item & Description</th>
              <th style="width: 15%">HSN/SAC</th>
              <th style="width: 10%" class="right-align-class">Qty</th>
              <th style="width: 15%" class="right-align-class">Rate</th>
              <th style="width: 15%" class="right-align-class">Amount</th>
            </tr>
          </thead>
          <tbody>
              {% for item in line_items %}
                <tr>
                  <td>{{ forloop.counter }}</td>
                  {% if notes.event == 'payg' %}
                    <td style="text-align: left">{{ item.description }}</td>
                  {% else %}
                    <td style="text-align: left">{{ item.name }}</td>
                  {% endif %}
                  <td>998319</td>
                  <td class="right-align-class">{{ item.quantity }}</td>
                  {% if notes.event == 'payg' %}
                    <td class="right-align-class">₹ {{ item.unit_price|format_amount }}</td>
                    <td class="right-align-class">₹ {{ item.amount|format_amount }}</td>
                  {% else %}
                    <td class="right-align-class">₹ {{ taxable_amount_in_rupees|format_amount }}</td>
                    <td class="right-align-class">₹ {{ taxable_amount_in_rupees|format_amount }}</td>
                  {% endif %}
                </tr>
              {% endfor %}
            <!-- Empty rows for spacing -->
            <tr>
              <td colspan="6" style="height: 100px; border: none"></td>
            </tr>
          </tbody>
        </table>

        <!-- Summary -->
        <div class="summary">
          <div class="total-words">
            <div class="margin-bottom-class">Total In Words</div>
            <div>
              <strong>
                {{ amount_in_words }} Only
              </strong>
            </div>
          </div>
          <div class="total-amounts">
            <div class="total-row">
              <div>Sub Total</div>
              <div>₹ {{ taxable_amount_in_rupees|format_amount }}</div>
            </div>
            {% if igst %}
              <div class="total-row">
                <div>IGST @ ({{ igst_percent }}%)</div>
                <div>₹ {{ igst_in_rupees|format_amount }}</div>
              </div>
            {% else %}
              <div class="total-row">
                <div>CGST @ ({{ cgst_percent }}%)</div>
                <div>₹ {{ cgst_in_rupees|format_amount }}</div>
              </div>
              <div class="total-row">
                <div>SGST @ ({{ sgst_percent }}%)</div>
                <div>₹ {{ sgst_in_rupees|format_amount }}</div>
              </div>
            {% endif %}
            <div class="total-row grand-total">
              <div>Total</div>
              <div>₹ {{ amount_in_rupees|format_amount }} </div>
            </div>
          </div>
        </div>

        <!-- Bank Details -->
        <div class="bank-details">
          <div class="bank-title">
            Beneficiary : Caliper Business Solutions Pvt. Ltd
          </div>
          <div>Bank Name : HDFC BANK LIMITED</div>
          <div>A/c No : **************</div>
          <div>Branch : 100 Feet/Indira Nagar</div>
          <div>IFSC code : HDFC0000547</div>
        </div>

        <!-- Notes -->
        <div class="notes">
          <div class="margin-bottom-class"><strong>Notes</strong></div>
          <div>Thanks for your business.</div>
        </div>

        <!-- Signature -->
        <div class="signature">
          <div class="company-bottom">
            Caliper Business Solutions Private Limited
          </div>
          
          <div class="signature-content">
            <!--
            <div class="stamp">
              DIGITAL<br />
              SIGNATURE<br />
              RAVI<br />
              MUMBAI
            </div>
            -->
            <!--<div class="sign">Signature</div>-->
          </div>
          <div class="signature-line">Authorised Signatory</div>
        </div>
        <!-- Page Number -->
        <!-- <div class="page-number">1</div> -->
      </div>
    </div>
  </body>
</html>
