"""
Enhanced Payment Exception Handling

This module provides a comprehensive exception hierarchy for payment processing
following modern industry standards for error handling and observability.
"""

import logging
from typing import Optional, Dict, Any
from pydantic import ValidationError
from enum import Enum
from utils import DateUtil

logger = logging.getLogger('application')


class PaymentErrorCode(Enum):
    """Standardized payment error codes for better error tracking and handling."""

    # Validation Errors (1000-1999)
    INVALID_PAYLOAD = "PAY_1001"
    MISSING_REQUIRED_FIELD = "PAY_1002"
    INVALID_AMOUNT = "PAY_1003"
    INVALID_CURRENCY = "PAY_1004"
    INVALID_CUSTOMER_DATA = "PAY_1005"

    # Security Errors (2000-2999)
    SIGNATURE_VERIFICATION_FAILED = "PAY_2001"
    UNAUTHORIZED_ACCESS = "PAY_2002"
    RATE_LIMIT_EXCEEDED = "PAY_2003"
    SUSPICIOUS_ACTIVITY = "PAY_2004"

    # Processing Errors (3000-3999)
    PAYMENT_GATEWAY_ERROR = "PAY_3001"
    SUBSCRIPTION_CREATION_FAILED = "PAY_3002"
    ORDER_CREATION_FAILED = "PAY_3003"
    PAYMENT_CAPTURE_FAILED = "PAY_3004"
    REFUND_FAILED = "PAY_3005"

    # Business Logic Errors (4000-4999)
    INSUFFICIENT_BALANCE = "PAY_4001"
    SUBSCRIPTION_ALREADY_ACTIVE = "PAY_4002"
    PLAN_NOT_AVAILABLE = "PAY_4003"
    UPGRADE_NOT_ALLOWED = "PAY_4004"
    TRIAL_ALREADY_USED = "PAY_4005"

    # System Errors (5000-5999)
    DATABASE_ERROR = "PAY_5001"
    EXTERNAL_SERVICE_UNAVAILABLE = "PAY_5002"
    TIMEOUT_ERROR = "PAY_5003"
    CONFIGURATION_ERROR = "PAY_5004"
    INTERNAL_SERVER_ERROR = "PAY_5005"


class BasePaymentException(Exception):
    """Base exception class for all payment-related errors."""

    def __init__(
        self,
        message: str,
        error_code: PaymentErrorCode,
        details: Optional[Dict[str, Any]] = None,
        user_message: Optional[str] = None,
        retry_after: Optional[int] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.user_message = user_message or self._get_default_user_message()
        self.retry_after = retry_after
        self.timestamp = self._get_current_timestamp()

        # Log the exception for monitoring
        self._log_exception()

    def _get_default_user_message(self) -> str:
        """Get user-friendly error message based on error code."""
        user_messages = {
            PaymentErrorCode.INVALID_PAYLOAD: "Please check your payment information and try again.",
            PaymentErrorCode.SIGNATURE_VERIFICATION_FAILED: "Payment verification failed. Please contact support.",
            PaymentErrorCode.PAYMENT_GATEWAY_ERROR: "Payment service is temporarily unavailable. Please try again later.",
            PaymentErrorCode.SUBSCRIPTION_ALREADY_ACTIVE: "You already have an active subscription.",
            PaymentErrorCode.PLAN_NOT_AVAILABLE: "The selected plan is not available. Please choose another plan.",
            PaymentErrorCode.INSUFFICIENT_BALANCE: "Insufficient balance to complete the transaction.",
            PaymentErrorCode.RATE_LIMIT_EXCEEDED: "Too many requests. Please try again later.",
            PaymentErrorCode.EXTERNAL_SERVICE_UNAVAILABLE: "Payment service is temporarily unavailable. Please try again later.",
        }
        return user_messages.get(self.error_code, "An error occurred while processing your payment. Please contact support.")

    def _get_current_timestamp(self) -> int:
        """Get current timestamp in milliseconds."""
        return DateUtil.get_current_timestamp()

    def _log_exception(self):
        """Log the exception with structured data."""
        logger.error(
            f"Payment Exception: {self.error_code.value}",
            extra={
                'error_code': self.error_code.value,
                'message': self.message,
                'details': self.details,
                'timestamp': self.timestamp,
                'exception_type': self.__class__.__name__
            }
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            'error_code': self.error_code.value,
            'message': self.user_message,
            'details': self.details,
            'timestamp': self.timestamp,
            'retry_after': self.retry_after
        }


class PaymentValidationError(BasePaymentException):
    """Exception raised for payment validation errors."""

    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        if field:
            kwargs.setdefault('details', {})['field'] = field

        error_code = kwargs.pop('error_code', PaymentErrorCode.INVALID_PAYLOAD)
        super().__init__(message, error_code, **kwargs)


class PaymentSecurityError(BasePaymentException):
    """Exception raised for payment security violations."""

    def __init__(self, message: str, **kwargs):
        error_code = kwargs.pop('error_code', PaymentErrorCode.SIGNATURE_VERIFICATION_FAILED)
        super().__init__(message, error_code, **kwargs)


class PaymentProcessingError(BasePaymentException):
    """Exception raised for payment processing errors."""

    def __init__(self, message: str, gateway_error: Optional[str] = None, **kwargs):
        if gateway_error:
            kwargs.setdefault('details', {})['gateway_error'] = gateway_error

        error_code = kwargs.pop('error_code', PaymentErrorCode.PAYMENT_GATEWAY_ERROR)
        super().__init__(message, error_code, **kwargs)


class PaymentBusinessLogicError(BasePaymentException):
    """Exception raised for business logic violations."""

    def __init__(self, message: str, **kwargs):
        error_code = kwargs.pop('error_code', PaymentErrorCode.PLAN_NOT_AVAILABLE)
        super().__init__(message, error_code, **kwargs)


class PaymentSystemError(BasePaymentException):
    """Exception raised for system-level errors."""

    def __init__(self, message: str, **kwargs):
        error_code = kwargs.pop('error_code', PaymentErrorCode.INTERNAL_SERVER_ERROR)
        super().__init__(message, error_code, **kwargs)


class PaymentRetryableError(BasePaymentException):
    """Exception for errors that can be retried."""

    def __init__(self, message: str, retry_after: int = 60, **kwargs):
        kwargs['retry_after'] = retry_after
        error_code = kwargs.pop('error_code', PaymentErrorCode.EXTERNAL_SERVICE_UNAVAILABLE)
        super().__init__(message, error_code, **kwargs)


class PaymentRateLimitError(PaymentSecurityError):
    """Exception raised when rate limits are exceeded."""

    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = 300, **kwargs):
        kwargs['retry_after'] = retry_after
        kwargs['error_code'] = PaymentErrorCode.RATE_LIMIT_EXCEEDED
        super().__init__(message, **kwargs)


def handle_payment_exception(func):
    """Decorator to handle payment exceptions and convert them to appropriate responses."""

    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except BasePaymentException:
            # Re-raise payment exceptions as-is
            raise
        except ValidationError as e:
            # Convert Pydantic validation errors
            raise PaymentValidationError(
                message=f"Validation failed: {str(e)}",
                details={'validation_errors': e.errors()}
            )
        except Exception as e:
            # Convert unexpected exceptions
            logger.exception(f"Unexpected error in {func.__name__}")
            raise PaymentSystemError(
                message=f"An unexpected error occurred: {str(e)}",
                details={'function': func.__name__}
            )

    return wrapper
