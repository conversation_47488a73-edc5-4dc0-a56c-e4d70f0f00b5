import logging
from typing import Dict, Any
from utils.mongo import MongoUtility
from utils.constants import (
    SequenceType,
    DBColls
)
from utils import (
    generate_sequence_no,
    DateUtil
)
from .request_validators import CreateOrderPayloadValidator
from schema import RazorpayOrderSchema
from .razorpay import <PERSON><PERSON><PERSON>y
from .exceptions import (
    PaymentProcessingError,
    handle_payment_exception
)
from .resilience import with_resilience
from .audit import PaymentAuditLogger

logger = logging.getLogger('application')
audit_logger = PaymentAuditLogger()


class PaygUtils(object):
    """
    Enhanced PAYG (Pay-as-you-go) utility class with modern industry standards.

    Provides secure, auditable, and resilient PAYG order processing
    with comprehensive error handling and monitoring.
    """

    def __init__(self, sclen):
        self.sclen = sclen
        self.db = MongoUtility()
        self.razorpay = Razorpay()
        self.now = DateUtil.get_current_timestamp()

        # Enhanced features
        self.audit_context = audit_logger.create_audit_context(
            user_id=getattr(sclen, 'user_id', None),
            company_id=getattr(sclen, 'company_id', None)
        )

    @handle_payment_exception
    @with_resilience(
        circuit_breaker_name="payg_order_creation",
        max_retries=3,
        failure_threshold=5,
        recovery_timeout=60
    )
    def create_order(self) -> Dict[str, Any]:
        """
        Create PAYG order with enhanced validation and error handling.

        Returns:
            Dict: Created order data

        Raises:
            PaymentProcessingError: If order creation fails
        """
        try:
            # Log order creation start
            self.audit_context.log_step("payg_order_creation", "started")

            items = self.sclen.payload.get('items', [])

            # Calculate total amount
            total_amount = 0
            for idx, item in enumerate(items):
                item_amount = item['amount']
                total_amount += item_amount

            # Calculate tax
            total_amount_with_tax = self.sclen.compute_tax(taxable_amount=total_amount, gst_details=False)

            # Create order object
            order_obj = self.razorpay.order.get_create_order_schema()
            order_obj['amount'] = self.sclen._process_amount(total_amount_with_tax)
            order_obj['receipt'] = generate_sequence_no(SequenceType.RECEIPT_NUMBER)
            order_obj['notes'].update({
                'user_id': self.sclen.user_id,
                'company_id': self.sclen.company_id,
                'subscription_id': self.sclen.current_subscription_id,
                'event': self.sclen.event,
                'total_excl_tax': self.sclen._process_amount(total_amount)
            })

            # Validate order data
            order_data = self.sclen.utility.validate_payload(CreateOrderPayloadValidator, order_obj)

            # Create order in Razorpay
            order_data, created = self.razorpay.order.create_order(order_data)

            if not created:
                error_code = order_data.get('error', {}).get('code', 'unknown')
                error_desc = order_data.get('error', {}).get('description', 'Unknown error')
                raise PaymentProcessingError(
                    f"Razorpay order creation failed: {error_code}: {error_desc}",
                    gateway_error=f"{error_code}: {error_desc}"
                )

            # Process and store order
            order_data['items'] = items
            order_data['order_no'] = self.now
            order_data = RazorpayOrderSchema(**order_data).model_dump()
            self.db.insert(DBColls.RAZORPAY_ORDERS, [order_data])
            order_data.pop('_id', None)

            # Set payment link
            self.sclen.utility.payment_link = order_data.get('short_url')

            # Log successful completion
            self.audit_context.log_step("payg_order_creation", "completed")

            return order_data

        except Exception as e:
            # Log creation failure
            self.audit_context.log_step("payg_order_creation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })
            raise