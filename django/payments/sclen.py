import copy
import logging
from typing import Dict, Any, Optional, List
from django.conf import settings
from utils.mongo import MongoUtility
from utils.constants import (
    RazorpayPaymentStatus,
    SubscriptionStatus,
    SubscriptionEvent,
    BILLING_CYCLES,
    BillingCycle,
    SequenceType,
    TrialAmount,
    EmailHeader,
    OrderStatus,
    PRECISSION,
    UsageKeys,
    UserType,
    UserRole,
    Currency,
    DBColls
)
from utils import (
    generate_sequence_no,
    number_to_words,
    DateUtil
)
from schema import (
    RazorpayCustomerSchema,
    RazorpayInvoiceSchema,
    RazorpayPlanSchema
)
from .request_validators import (
    CreateRazorpayCustomerPayloadValidator,
    CreateRazorpayPlanValidator,
)
from .razorpay import Razorpay
from .subscription_utils import SubscriptionUtils
from .payg_utils import PaygUtils
from .payment_utils import (
    send_invoice_to_email,
    compute_gst_details,
    back_compute_tax,
    send_payment_sms,
    convert_to_ms,
    Finder
)
from .exceptions import (
    PaymentProcessingError,
    PaymentValidationError,
    handle_payment_exception
)
from .resilience import with_resilience
from .audit import PaymentAuditLogger

logger = logging.getLogger('application')
audit_logger = PaymentAuditLogger()


class Sclen(object):
    """
    Enhanced Sclen class for secure payment processing.

    Handles all Sclen & Razorpay interactions with modern industry standards:
        1. Subscription (Subscribe & Upgrade) Payments
        2. Process PAYG (Pay-as-you-go) Payments
        3. Enhanced security and audit logging
        4. Resilience patterns and error handling
    """

    def __init__(self, utility):
        self.now = DateUtil.get_current_timestamp()
        self.razorpay = Razorpay()
        self.db = MongoUtility()
        self.utility = utility

        # Request and payload properties
        self._request = None
        self._payload = None
        self._event = None
        self._upgrade_plan = False
        self._is_payg = False
        self._upgrade_addons = []
        self._new_plan_id = None
        self._current_subscription_id = None

        # Razorpay entity IDs
        self.razorpay_customer_id = None
        self.razorpay_current_plan_id = None
        self.razorpay_new_plan_id = None

        # Enhanced features
        self.audit_context = audit_logger.create_audit_context(
            user_id=self.user_id,
            company_id=self.company_id
        )

        # Initialize processing
        self._initialize_sclen_processing()

    def _initialize_sclen_processing(self):
        """Initialize Sclen processing with validation and audit logging."""
        try:
            # Validate utility object
            if not hasattr(self.utility, '__class__'):
                raise PaymentValidationError(
                    f'Utility expected to be an instance of class, got {type(self.utility)}'
                )

        except Exception as e:
            self.audit_context.log_step("sclen_initialization", "failed", {
                'error': str(e)
            })
            raise

    @property
    def request(self):
        """Get request object with enhanced validation."""
        if not self._request:
            if not hasattr(self.utility, '__class__'):
                raise PaymentValidationError(
                    f'Utility expected to be an instance of class, got {type(self.utility)}'
                )

            if not hasattr(self.utility, 'request'):
                raise PaymentValidationError(
                    f"'{self.utility.__class__.__name__}' object has no attribute 'request'"
                )

            self._request = getattr(self.utility, 'request', None)

            if self._request is None:
                raise PaymentValidationError("Request object is None")

        return self._request

    @property
    def payload(self):
        if not self._payload:
            if not hasattr(self.utility, '__class__'):
                raise AttributeError(
                    f'`utility` expected to be an instance of class, got {type(self.utility)}')

            if not hasattr(self.utility, 'payload'):
                raise AttributeError(
                    f"'{self.utility.__class__.__name__}' object has no attribute 'payload'")

            self._payload = getattr(self.utility, 'payload', None)
        return self._payload

    @property
    def event(self):
        if not self._event:
            self._event = self.payload.get('event')
        return self._event

    @property
    def upgrade_plan(self):
        if not self._upgrade_plan:
            self._upgrade_plan = (self.event == SubscriptionEvent.UPGRADE)
        return self._upgrade_plan

    @property
    def is_payg(self):
        if not self._is_payg:
            self._is_payg = (self.event == SubscriptionEvent.PAYG)
        return self._is_payg

    @property
    def upgrade_addons(self):
        if not self._upgrade_addons:
            self._upgrade_addons = self.payload.get('addons', [])
        return self._upgrade_addons

    @property
    def company_id(self):
        return self.request.company_id

    @property
    def user_id(self):
        return self.request.user_id

    @property
    def current_subscription_id(self):
        if not self._current_subscription_id:
            self._current_subscription_id = self.payload.get('current_subscription_id')
        return self._current_subscription_id

    @property
    def new_plan_id(self):
        if not self._new_plan_id:
            self._new_plan_id = self.payload.get('new_plan_id')
        return self._new_plan_id

    def _get_utility(self) -> type:
        """
        Get appropriate utility class based on billing cycle with enhanced validation.

        Returns:
            type: SubscriptionUtils class for supported billing cycles

        Raises:
            PaymentValidationError: If billing cycle is invalid
            PaymentProcessingError: If utility cannot be determined
        """
        try:
            # Validate current subscription exists
            if not hasattr(self, 'current_subscription') or not self.current_subscription:
                raise PaymentValidationError("Current subscription not available for utility selection")

            # Extract and validate billing cycle
            billing_cycle = self.current_subscription.get('billing_cycle')
            if not billing_cycle:
                raise PaymentValidationError("Billing cycle not found in current subscription")

            # Validate supported billing cycles
            supported_cycles = [
                BillingCycle.DAILY,
                BillingCycle.WEEKLY,
                BillingCycle.MONTHLY,
                BillingCycle.ANNUALLY
            ]

            if billing_cycle not in supported_cycles:
                supported_cycle_names = [cycle for cycle in supported_cycles]
                raise PaymentValidationError(
                    f"Unsupported billing cycle: {billing_cycle}. Supported cycles: {', '.join(supported_cycle_names)}"
                )

            return SubscriptionUtils

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("utility_selection", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Failed to determine utility class: {str(e)}",
                details={'original_error': str(e)}
            )

    def _simulate_live_mode(self) -> Dict[str, Any]:
        """
        Simulate live mode with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Payment processing result

        Raises:
            PaymentValidationError: If live mode simulation setup is invalid
            PaymentProcessingError: If live mode processing fails
        """
        try:
            # Log live mode simulation start
            self.audit_context.log_step("live_mode_simulation", "started", {
                'company_id': self.company_id,
                'subscription_id': self.current_subscription_id,
            })

            # Ensure company and subscription data are available
            try:
                self._get_company()
                self._check_if_valid_subscription()
            except Exception as validation_error:
                raise PaymentProcessingError(
                    f"Failed to validate company/subscription for live mode: {str(validation_error)}",
                    details={'validation_error': str(validation_error)}
                )

            # Get test live mode plan
            try:
                query = {
                    'test_live_mode': True,
                    'is_active': True
                }
                self.current_plan = self.db.find(DBColls.PLANS, query, find_one=True)

                if not self.current_plan:
                    raise PaymentValidationError(
                        "Test live mode plan unavailable or improperly configured",
                        details={'query': query}
                    )

            except Exception as plan_error:
                raise PaymentProcessingError(
                    f"Failed to get test live mode plan: {str(plan_error)}",
                    details={'plan_error': str(plan_error)}
                )

            # Get or create current Razorpay plan
            try:
                self.razorpay_current_plan_id = self.current_plan.get('razorpay_plan_id')

                if not self.razorpay_current_plan_id:
                    self.razorpay_current_plan = self._create_razorpay_plan(self.current_plan)
                else:
                    query = {'id': self.razorpay_current_plan_id}
                    self.razorpay_current_plan = self.db.find(
                        DBColls.RAZORPAY_PLANS, query, find_one=True
                    )

                    if not self.razorpay_current_plan:
                        # Create if not found
                        self.razorpay_current_plan = self._create_razorpay_plan(self.current_plan)

                self.razorpay_current_plan_id = self.razorpay_current_plan['id']

            except Exception as current_plan_error:
                raise PaymentProcessingError(
                    f"Failed to setup current Razorpay plan: {str(current_plan_error)}",
                    details={'current_plan_error': str(current_plan_error)}
                )

            # Handle upgrade scenario
            if self.upgrade_plan:
                try:
                    # Get test upgrade plan
                    query = {
                        'test_live_mode': True,
                        'test_upgrade_plan': True,
                        'is_active': True
                    }
                    self.new_plan = self.db.find(DBColls.PLANS, query, find_one=True)

                    if not self.new_plan:
                        raise PaymentValidationError(
                            "Test live mode upgrade plan unavailable or improperly configured",
                            details={'query': query}
                        )

                    # Process upgrade addons
                    self.pre_process_upgrade_addons()

                    # Get or create new Razorpay plan
                    self.razorpay_new_plan_id = self.new_plan.get('razorpay_plan_id')

                    if not self.razorpay_new_plan_id:
                        self.razorpay_new_plan = self._create_razorpay_plan(self.new_plan)
                    else:
                        query = {'id': self.razorpay_new_plan_id}
                        self.razorpay_new_plan = self.db.find(
                            DBColls.RAZORPAY_PLANS, query, find_one=True
                        )

                        if not self.razorpay_new_plan:
                            # Create if not found
                            self.razorpay_new_plan = self._create_razorpay_plan(self.new_plan)

                    self.razorpay_new_plan_id = self.razorpay_new_plan['id']

                    # Log upgrade setup
                    self.audit_context.log_step("live_mode_simulation", "upgrade_setup_completed", {
                        'new_plan_id': self.new_plan.get('id'),
                        'razorpay_new_plan_id': self.razorpay_new_plan_id
                    })

                    # Execute upgrade
                    return self.upgrade()

                except Exception as upgrade_error:
                    raise PaymentProcessingError(
                        f"Failed to setup upgrade in live mode: {str(upgrade_error)}",
                        details={'upgrade_error': str(upgrade_error)}
                    )

            # Execute subscription
            try:
                self.audit_context.log_step("live_mode_simulation", "subscription_setup_completed", {
                    'current_plan_id': self.current_plan.get('id'),
                    'razorpay_current_plan_id': self.razorpay_current_plan_id
                })

                return self.subscribe()

            except Exception as subscribe_error:
                raise PaymentProcessingError(
                    f"Failed to execute subscription in live mode: {str(subscribe_error)}",
                    details={'subscribe_error': str(subscribe_error)}
                )

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("live_mode_simulation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Live mode simulation failed: {str(e)}",
                details={'original_error': str(e)}
            )

    def _get_company(self) -> Dict[str, Any]:
        """
        Retrieve company information with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Company information

        Raises:
            PaymentValidationError: If company ID is invalid or company not found
            PaymentProcessingError: If company retrieval fails
        """
        try:
            # Query company from database
            query = {'id': self.company_id}
            self.company = self.db.find(DBColls.COMPANIES, query, find_one=True)

            # Validate company exists
            if not self.company:
                raise PaymentValidationError(
                    f"Company not found with ID: {self.company_id}",
                    details={'company_id': self.company_id}
                )

            # Extract Razorpay customer ID
            self.razorpay_customer_id = self.company.get('razorpay_customer_id')

            return self.company

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("company_retrieval", "failed", {
                'company_id': self.company_id,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Failed to retrieve company: {str(e)}",
                details={'company_id': self.company_id, 'original_error': str(e)}
            )

    def _check_if_valid_subscription(self) -> bool:
        """
        Validate current subscription with enhanced error handling and security checks.

        Returns:
            bool: True if subscription is valid

        Raises:
            PaymentValidationError: If subscription validation fails
            PaymentProcessingError: If subscription retrieval fails
        """
        try:
            # Validate subscription ID
            if not self.current_subscription_id:
                raise PaymentValidationError(
                    "Current subscription ID is required for validation",
                    details={'subscription_id': self.current_subscription_id}
                )

            # Validate company ID
            if not self.company_id:
                raise PaymentValidationError(
                    "Company ID is required for subscription validation",
                    details={'company_id': self.company_id}
                )

            # Query subscription from database
            query = {
                'id': self.current_subscription_id,
                'company_id': self.company_id
            }

            self.current_subscription = self.db.find(
                DBColls.SUBSCRIPTIONS, query, find_one=True
            )

            # Validate subscription exists
            if not self.current_subscription:
                raise PaymentValidationError(
                    "Subscription not found or not associated with company",
                    details={
                        'subscription_id': self.current_subscription_id,
                        'company_id': self.company_id
                    }
                )

            # Validate subscription belongs to the correct company
            subscription_company_id = self.current_subscription.get('company_id')
            if subscription_company_id != self.company_id:
                raise PaymentValidationError(
                    "Subscription does not belong to the specified company",
                    details={
                        'subscription_company_id': subscription_company_id,
                        'expected_company_id': self.company_id
                    }
                )

            return True

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("subscription_validation", "failed", {
                'subscription_id': self.current_subscription_id,
                'company_id': self.company_id,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Failed to validate subscription: {str(e)}",
                details={
                    'subscription_id': self.current_subscription_id,
                    'company_id': self.company_id,
                    'original_error': str(e)
                }
            )

    def _check_if_valid_plan(self) -> bool:
        """
        Validate current and new plans with enhanced error handling and security checks.

        Returns:
            bool: True if plans are valid

        Raises:
            PaymentValidationError: If plan validation fails
            PaymentProcessingError: If plan retrieval fails
        """
        try:

            # Validate current subscription exists
            if not hasattr(self, 'current_subscription') or not self.current_subscription:
                raise PaymentValidationError(
                    "Current subscription is required for plan validation",
                    details={'has_current_subscription': hasattr(self, 'current_subscription')}
                )

            # Get current plan ID
            current_plan_id = self.current_subscription.get('plan_id')
            if not current_plan_id:
                raise PaymentValidationError(
                    "Current subscription missing plan_id",
                    details={'subscription_keys': list(self.current_subscription.keys())}
                )

            # Validate current plan
            try:
                query = {
                    'id': current_plan_id,
                    'is_active': True
                }
                self.current_plan = self.db.find(DBColls.PLANS, query, find_one=True)

                if not self.current_plan:
                    raise PaymentValidationError(
                        f"Current plan not found or inactive: {current_plan_id}",
                        details={'plan_id': current_plan_id}
                    )

                self.razorpay_current_plan_id = self.current_plan.get('razorpay_plan_id')

            except Exception as current_plan_error:
                raise PaymentProcessingError(
                    f"Failed to validate current plan: {str(current_plan_error)}",
                    details={'plan_id': current_plan_id, 'error': str(current_plan_error)}
                )

            # Validate new plan if upgrade
            if self.upgrade_plan:
                try:
                    # Validate new plan ID exists
                    if not self.new_plan_id:
                        raise PaymentValidationError(
                            "New plan ID is required for upgrade",
                            details={'new_plan_id': self.new_plan_id}
                        )

                    # Query new plan
                    query['id'] = self.new_plan_id
                    self.new_plan = self.db.find(DBColls.PLANS, query, find_one=True)

                    if not self.new_plan:
                        raise PaymentValidationError(
                            f"New plan not found or inactive: {self.new_plan_id}",
                            details={'new_plan_id': self.new_plan_id}
                        )

                    # Validate upgrade logic (new plan should be different)
                    if self.new_plan_id == current_plan_id:
                        raise PaymentValidationError(
                            "Cannot upgrade to the same plan",
                            details={'current_plan_id': current_plan_id, 'new_plan_id': self.new_plan_id}
                        )

                    # Process upgrade addons
                    try:
                        self.pre_process_upgrade_addons()
                    except Exception as addon_error:
                        raise PaymentProcessingError(
                            f"Failed to process upgrade addons: {str(addon_error)}",
                            details={'addon_error': str(addon_error)}
                        )

                    self.razorpay_new_plan_id = self.new_plan.get('razorpay_plan_id')

                except Exception as new_plan_error:
                    raise PaymentProcessingError(
                        f"Failed to validate new plan: {str(new_plan_error)}",
                        details={'new_plan_id': self.new_plan_id, 'error': str(new_plan_error)}
                    )

            return True

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("plan_validation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Plan validation failed: {str(e)}",
                details={'original_error': str(e)}
            )

    def pre_process_upgrade_addons(self) -> Dict[str, Any]:
        """
        Pre-process upgrade addons with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Updated new plan with processed addons

        Raises:
            PaymentValidationError: If addon data is invalid
            PaymentProcessingError: If addon processing fails
        """
        try:
            # Validate new plan exists
            if not hasattr(self, 'new_plan') or not self.new_plan:
                raise PaymentValidationError("New plan is required for addon processing")

            # Validate upgrade addons
            if not hasattr(self, 'upgrade_addons'):
                self.upgrade_addons = []

            # Create addon quantity mapping with validation
            addon_vs_qty = {}
            for i, ua in enumerate(self.upgrade_addons):

                addon_id = ua.get('id')
                addon_qty = ua.get('qty')

                if addon_qty <= 0:
                    raise PaymentValidationError("Invalid addon quantity at index")

                addon_vs_qty[addon_id] = addon_qty

            # Process plan addons
            addons = []
            plan_addons = self.new_plan.get('addons', [])

            for i, addon in enumerate(plan_addons):
                try:
                    addon_id = addon.get('id')
                    # Check if this addon is being upgraded
                    addon_qty = addon_vs_qty.get(addon_id)
                    if not addon_qty:
                        continue

                    # Process addon
                    addon['qty'] = addon_qty
                    addon['is_enabled'] = True
                    addon['is_paid'] = False
                    addons.append(addon)

                except Exception as addon_error:
                    raise PaymentProcessingError(
                        f"Failed to process addon at index {i}: {str(addon_error)}",
                        details={'addon_index': i, 'addon_error': str(addon_error)}
                    )

            # Update new plan with processed addons
            self.new_plan['addons'] = addons

            return self.new_plan

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("addon_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Addon processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    def _get_plan_description(self, plan: Dict[str, Any]) -> str:
        """
        Generate plan description with enhanced validation and error handling.

        Args:
            plan: Plan data dictionary

        Returns:
            str: Formatted plan description

        Raises:
            PaymentValidationError: If plan data is invalid
        """
        try:
            # Validate required fields
            product = plan.get('product_name')
            label = plan.get('billing_cycle_label')

            # Generate description
            description = f'{product} Module ({label})'

            # Validate generated description
            if not description or len(description.strip()) == 0:
                raise PaymentValidationError("Generated plan description is empty")

            return description

        except PaymentValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            raise PaymentValidationError(
                f"Failed to generate plan description: {str(e)}",
                details={'plan_id': plan.get('id'), 'original_error': str(e)}
            )

    def _get_plan_name(self, plan: Dict[str, Any]) -> str:
        """
        Generate plan name with enhanced validation and error handling.

        Args:
            plan: Plan data dictionary

        Returns:
            str: Formatted plan name

        Raises:
            PaymentValidationError: If plan data is invalid
        """
        try:
            # Validate required fields
            product_name = plan.get('product_name')
            plan_name = plan.get('plan_name')

            # Generate plan name
            formatted_name = f'{product_name} - {plan_name}'

            # Validate generated name
            if not formatted_name or len(formatted_name.strip()) == 0:
                raise PaymentValidationError("Generated plan name is empty")

            return formatted_name

        except PaymentValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            raise PaymentValidationError(
                f"Failed to generate plan name: {str(e)}",
                details={'plan_id': plan.get('id'), 'original_error': str(e)}
            )

    def _get_prorated_description(self, plan: Dict[str, Any]) -> str:
        """
        Generate prorated upgrade description with enhanced validation and error handling.

        Args:
            plan: Plan data dictionary

        Returns:
            str: Formatted prorated description

        Raises:
            PaymentValidationError: If plan data is invalid
        """
        try:
            # Validate required field
            plan_name = plan.get('plan_name')
            if not plan_name:
                raise PaymentValidationError(
                    "Plan missing plan_name for prorated description generation",
                    details={'plan_keys': list(plan.keys())}
                )

            # Generate prorated description
            description = (
                f"Upgrading to {plan_name} with "
                "prorated charges for the remaining billing "
                "period."
            )

            return description

        except PaymentValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            raise PaymentValidationError(
                f"Failed to generate prorated description: {str(e)}",
                details={'plan_id': plan.get('id'), 'original_error': str(e)}
            )

    def _process_amount(self, amount: float) -> int:
        """
        Process amount to paise with enhanced validation and error handling.

        Args:
            amount: Amount in rupees

        Returns:
            int: Amount in paise (whole number)

        Raises:
            PaymentValidationError: If amount is invalid
        """
        try:
            # Validate amount type
            if not isinstance(amount, (int, float)):
                raise PaymentValidationError(
                    f"Invalid amount type: expected int or float, got {type(amount).__name__}",
                    details={'amount': amount, 'amount_type': type(amount).__name__}
                )

            # Validate amount value
            if amount < 0:
                raise PaymentValidationError(
                    f"Amount cannot be negative: {amount}",
                    details={'amount': amount}
                )

            if amount == 0:
                raise PaymentValidationError(
                    "Amount cannot be zero",
                    details={'amount': amount}
                )

            # Check for reasonable amount limits
            if amount > 10000000:  # 1 crore rupees
                raise PaymentValidationError(
                    f"Amount exceeds maximum limit: {amount}",
                    details={'amount': amount, 'max_limit': 10000000}
                )

            # Round to precision
            try:
                rounded_amount = round(amount, PRECISSION)
            except Exception as round_error:
                raise PaymentValidationError(
                    f"Failed to round amount: {str(round_error)}",
                    details={'amount': amount, 'precision': PRECISSION}
                )

            # Convert to paise (multiply by 100)
            try:
                paise_amount = int(rounded_amount * 100)
            except (ValueError, OverflowError) as convert_error:
                raise PaymentValidationError(
                    f"Failed to convert amount to paise: {str(convert_error)}",
                    details={'rounded_amount': rounded_amount}
                )

            # Validate final paise amount
            if paise_amount <= 0:
                raise PaymentValidationError(
                    f"Converted paise amount is invalid: {paise_amount}",
                    details={'original_amount': amount, 'paise_amount': paise_amount}
                )

            return paise_amount

        except PaymentValidationError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            raise PaymentValidationError(
                f"Amount processing failed: {str(e)}",
                details={'amount': amount, 'original_error': str(e)}
            )

    def _process_addons(self, entity: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process addons with enhanced validation and error handling.

        Args:
            entity: Entity containing addons data

        Returns:
            List[Dict[str, Any]]: Processed addon objects

        Raises:
            PaymentValidationError: If addon data is invalid
            PaymentProcessingError: If addon processing fails
        """
        try:
            # Log addon processing start
            self.audit_context.log_step("addon_processing", "started", {
                'entity_type': type(entity).__name__,
                'has_addons': 'addons' in entity if isinstance(entity, dict) else False
            })

            # Validate entity
            if not isinstance(entity, dict):
                raise PaymentValidationError(
                    f"Invalid entity format: expected dict, got {type(entity).__name__}",
                    details={'entity_type': type(entity).__name__}
                )

            # Get plan addons
            plan_addons = entity.get('addons', [])

            # Validate addons structure
            if not isinstance(plan_addons, list):
                raise PaymentValidationError(
                    f"Invalid addons format: expected list, got {type(plan_addons).__name__}",
                    details={'addons_type': type(plan_addons).__name__}
                )

            addons = []
            processed_count = 0
            skipped_count = 0

            for i, addon in enumerate(plan_addons):
                try:
                    # Validate addon structure
                    if not isinstance(addon, dict):
                        raise PaymentValidationError(
                            f"Invalid addon format at index {i}: expected dict, got {type(addon).__name__}",
                            details={'addon_index': i, 'addon_type': type(addon).__name__}
                        )

                    # Skip paid addons
                    if addon.get('is_paid'):
                        skipped_count += 1
                        continue

                    # Skip disabled addons
                    if not addon.get('is_enabled'):
                        skipped_count += 1
                        continue

                    # Validate addon price
                    price = addon.get('price')
                    if not price:
                        skipped_count += 1
                        continue

                    if not isinstance(price, (int, float)) or price < 0:
                        raise PaymentValidationError(
                            f"Invalid addon price at index {i}: {price}",
                            details={'addon_index': i, 'price': price}
                        )

                    # Validate required addon fields
                    required_fields = ['id', 'name']
                    missing_fields = []

                    for field in required_fields:
                        if not addon.get(field):
                            missing_fields.append(field)

                    if missing_fields:
                        raise PaymentValidationError(
                            f"Addon at index {i} missing required fields: {', '.join(missing_fields)}",
                            details={'addon_index': i, 'missing_fields': missing_fields}
                        )

                    # Compute addon price with tax
                    try:
                        price = self.compute_tax(taxable_amount=price, gst_details=False)
                    except Exception as tax_error:
                        raise PaymentProcessingError(
                            f"Failed to compute addon tax at index {i}: {str(tax_error)}",
                            details={'addon_index': i, 'tax_error': str(tax_error)}
                        )

                    # Get addon schema
                    try:
                        addon_obj = self.razorpay.subscription.get_subscription_addon_schema()
                    except Exception as schema_error:
                        raise PaymentProcessingError(
                            f"Failed to get addon schema for index {i}: {str(schema_error)}",
                            details={'addon_index': i, 'schema_error': str(schema_error)}
                        )

                    # Validate addon schema structure
                    if not isinstance(addon_obj, dict):
                        raise PaymentProcessingError(
                            f"Invalid addon schema format: expected dict, got {type(addon_obj).__name__}",
                            details={'addon_index': i, 'schema_type': type(addon_obj).__name__}
                        )

                    # Set addon item details
                    if 'item' not in addon_obj:
                        addon_obj['item'] = {}

                    try:
                        addon_obj['item'].update({
                            'name': addon['id'],
                            'amount': self._process_amount(price),
                            'currency': entity.get('currency', Currency.INR),
                            'description': addon['name']
                        })
                    except Exception as item_error:
                        raise PaymentProcessingError(
                            f"Failed to set addon item details for index {i}: {str(item_error)}",
                            details={'addon_index': i, 'item_error': str(item_error)}
                        )

                    # Set addon quantity
                    addon_qty = addon.get('qty', 1)
                    if not isinstance(addon_qty, (int, float)) or addon_qty <= 0:
                        raise PaymentValidationError(
                            f"Invalid addon quantity at index {i}: {addon_qty}",
                            details={'addon_index': i, 'quantity': addon_qty}
                        )

                    addon_obj['quantity'] = int(addon_qty)
                    addons.append(addon_obj)
                    processed_count += 1

                except Exception as addon_error:
                    raise PaymentProcessingError(
                        f"Failed to process addon at index {i}: {str(addon_error)}",
                        details={'addon_index': i, 'addon_error': str(addon_error)}
                    )

            # Log successful processing
            self.audit_context.log_step("addon_processing", "completed", {
                'total_addons': len(plan_addons),
                'processed_addons': processed_count,
                'skipped_addons': skipped_count
            })

            return addons

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("addon_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Addon processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    def _create_addon_references(self, entity: Dict[str, Any], plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create addon references with enhanced validation and error handling.

        Args:
            entity: Entity containing addons data
            plan_data: Plan data to update with addon references

        Returns:
            Dict[str, Any]: Updated plan data with addon references

        Raises:
            PaymentValidationError: If addon reference data is invalid
            PaymentProcessingError: If addon reference creation fails
        """
        try:
            # Log addon reference creation start
            self.audit_context.log_step("addon_reference_creation", "started", {
                'entity_type': type(entity).__name__,
                'plan_data_type': type(plan_data).__name__
            })

            # Validate entity
            if not isinstance(entity, dict):
                raise PaymentValidationError(
                    f"Invalid entity format: expected dict, got {type(entity).__name__}",
                    details={'entity_type': type(entity).__name__}
                )

            # Validate plan data
            if not isinstance(plan_data, dict):
                raise PaymentValidationError(
                    f"Invalid plan data format: expected dict, got {type(plan_data).__name__}",
                    details={'plan_data_type': type(plan_data).__name__}
                )

            # Get addons from entity
            addons = entity.get('addons', [])

            # Validate addons structure
            if not isinstance(addons, list):
                raise PaymentValidationError(
                    f"Invalid addons format: expected list, got {type(addons).__name__}",
                    details={'addons_type': type(addons).__name__}
                )

            # Create features mapping
            features = {}
            processed_count = 0

            for i, addon in enumerate(addons):
                try:
                    # Validate addon structure
                    if not isinstance(addon, dict):
                        raise PaymentValidationError(
                            f"Invalid addon format at index {i}: expected dict, got {type(addon).__name__}",
                            details={'addon_index': i, 'addon_type': type(addon).__name__}
                        )

                    # Validate required addon fields
                    addon_id = addon.get('id')
                    addon_name = addon.get('name')

                    if not addon_id:
                        raise PaymentValidationError(
                            f"Addon at index {i} missing ID",
                            details={'addon_index': i, 'addon_keys': list(addon.keys())}
                        )

                    if not addon_name:
                        raise PaymentValidationError(
                            f"Addon at index {i} missing name",
                            details={'addon_index': i, 'addon_id': addon_id}
                        )

                    # Add to features mapping
                    features[addon_id] = addon_name
                    processed_count += 1

                except Exception as addon_error:
                    raise PaymentProcessingError(
                        f"Failed to process addon reference at index {i}: {str(addon_error)}",
                        details={'addon_index': i, 'addon_error': str(addon_error)}
                    )

            # Update plan data with features if any exist
            if features:
                # Ensure notes section exists
                if 'notes' not in plan_data:
                    plan_data['notes'] = {}

                # Validate notes structure
                if not isinstance(plan_data['notes'], dict):
                    raise PaymentValidationError(
                        f"Invalid plan notes format: expected dict, got {type(plan_data['notes']).__name__}",
                        details={'notes_type': type(plan_data['notes']).__name__}
                    )

                # Update notes with features
                try:
                    plan_data['notes'].update(features)
                except Exception as update_error:
                    raise PaymentProcessingError(
                        f"Failed to update plan notes with features: {str(update_error)}",
                        details={'update_error': str(update_error)}
                    )

            # Log successful creation
            self.audit_context.log_step("addon_reference_creation", "completed", {
                'total_addons': len(addons),
                'processed_references': processed_count,
                'features_added': len(features)
            })

            return plan_data

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("addon_reference_creation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Addon reference creation failed: {str(e)}",
                details={'original_error': str(e)}
            )

    def _get_billing_cycles(self, entity: Dict[str, Any]) -> int:
        """
        Get billing cycles count with enhanced validation and error handling.

        Args:
            entity: Entity containing billing cycle information

        Returns:
            int: Number of billing cycles

        Raises:
            PaymentValidationError: If billing cycle is invalid
            PaymentProcessingError: If billing cycle calculation fails
        """
        try:
            # Get billing cycle
            billing_cycle = entity.get('billing_cycle')
            if not billing_cycle:
                raise PaymentValidationError("Entity missing billing_cycle for cycle calculation")

            # Validate billing cycle is supported
            supported_cycles = [
                BillingCycle.DAILY,
                BillingCycle.WEEKLY,
                BillingCycle.MONTHLY,
                BillingCycle.ANNUALLY
            ]

            if billing_cycle not in supported_cycles:
                supported_cycle_names = [cycle for cycle in supported_cycles]
                raise PaymentValidationError(
                    f"Unsupported billing cycle: {billing_cycle}. Supported cycles: {', '.join(supported_cycle_names)}"
                )

            # Get base billing cycle count
            try:
                base_cycles = BILLING_CYCLES[billing_cycle]
            except KeyError:
                raise PaymentValidationError(f"Billing cycle not found in BILLING_CYCLES: {billing_cycle}")

            # Handle daily and weekly cycles (return as-is)
            if billing_cycle in [BillingCycle.DAILY, BillingCycle.WEEKLY]:
                return int(base_cycles)

            # Handle monthly and annual cycles (multiply by billing years)
            try:
                billing_years = settings.BILLING_YEARS
            except AttributeError:
                raise PaymentProcessingError("BILLING_YEARS not configured in settings")

            # Calculate total cycles
            try:
                total_cycles = int(base_cycles * billing_years)
            except (ValueError, OverflowError) as calc_error:
                raise PaymentProcessingError(
                    f"Failed to calculate total billing cycles: {str(calc_error)}",
                    details={
                        'base_cycles': base_cycles,
                        'billing_years': billing_years,
                        'calc_error': str(calc_error)
                    }
                )

            # Validate final result
            if total_cycles <= 0:
                raise PaymentValidationError(
                    f"Calculated billing cycles is invalid: {total_cycles}",
                    details={
                        'billing_cycle': str(billing_cycle),
                        'base_cycles': base_cycles,
                        'billing_years': billing_years
                    }
                )

            return total_cycles

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            raise PaymentProcessingError(
                f"Billing cycles calculation failed: {str(e)}",
                details={'billing_cycle': entity.get('billing_cycle'), 'original_error': str(e)}
            )

    def _create_customer(self) -> Dict[str, Any]:
        """
        Create Razorpay customer with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Created customer data

        Raises:
            PaymentValidationError: If customer data is invalid
            PaymentProcessingError: If customer creation fails
        """
        try:
            # Validate company data exists
            if not self.company:
                raise PaymentValidationError("Company data is required for customer creation")

            # Get customer schema and populate basic data
            customer_obj = self.razorpay.customer.get_create_customer_schema()

            # Populate customer basic information
            customer_obj.update({
                'name': self.company.get('name'),
                'email': self.company.get('email'),
                'contact': str(self.company.get('phone') or ''),
                'gstin': self.company.get('gstin')
            })

            # Populate customer notes with additional company information
            customer_obj['notes'].update({
                'pan': self.company.get('pan'),
                'address': self.company.get('address'),
                'state': self.company.get('state'),
                'city': self.company.get('city'),
                'pincode': str(self.company.get('pincode') or ''),
                'company_type': self.company.get('company_type'),
                'company_id': self.company.get('id')
            })

            # Validate customer payload
            try:
                customer_data = self.utility.validate_payload(
                    CreateRazorpayCustomerPayloadValidator, customer_obj
                )
            except Exception as validation_error:
                raise PaymentValidationError(
                    f"Customer payload validation failed: {str(validation_error)}",
                    details={'validation_error': str(validation_error)}
                )

            # Create customer in Razorpay
            try:
                razorpay_customer, created = self.razorpay.customer.create_customer(customer_data)
            except Exception as creation_error:
                raise PaymentProcessingError(
                    f"Razorpay customer creation failed: {str(creation_error)}",
                    details={'creation_error': str(creation_error)}
                )

            # Validate customer creation success
            if not created:
                error_info = razorpay_customer.get('error', {})
                error_code = error_info.get('code', 'unknown')
                error_desc = error_info.get('description', 'Unknown error')

                self.audit_context.log_step("customer_creation", "razorpay_failed", {
                    'error_code': error_code,
                    'error_description': error_desc
                })

                raise PaymentProcessingError(
                    f"Razorpay customer creation failed: {error_code}: {error_desc}",
                    details={'error_code': error_code, 'error_description': error_desc}
                )

            # Validate and save customer data
            try:
                razorpay_customer = RazorpayCustomerSchema(**razorpay_customer).model_dump()
                self.db.insert(DBColls.RAZORPAY_CUSTOMERS, [razorpay_customer])
                razorpay_customer.pop('_id', None)
            except Exception as save_error:
                raise PaymentProcessingError(
                    f"Failed to save customer data: {str(save_error)}",
                    details={'save_error': str(save_error)}
                )

            return razorpay_customer

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("customer_creation", "failed", {
                'company_id': self.company_id,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Customer creation failed: {str(e)}",
                details={'company_id': self.company_id, 'original_error': str(e)}
            )

    def _get_or_create_customer(self) -> Dict[str, Any]:
        """
        Get or create Razorpay customer with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Customer data

        Raises:
            PaymentValidationError: If customer data is invalid
            PaymentProcessingError: If customer creation/retrieval fails
        """
        try:
            # Try to get existing customer
            if self.razorpay_customer_id:
                try:
                    # First check local database
                    query = {'id': self.razorpay_customer_id}
                    self.razorpay_customer = self.db.find(
                        DBColls.RAZORPAY_CUSTOMERS, query, find_one=True
                    )

                    if not self.razorpay_customer:
                        # If not found locally, fetch from Razorpay
                        self.razorpay_customer, fetched = self.razorpay.customer.get_customers(
                            self.razorpay_customer_id
                        )

                        if not fetched:
                            error_info = self.razorpay_customer.get('error', {})
                            error_code = error_info.get('code', 'unknown')
                            error_desc = error_info.get('description', 'Unknown error')

                            self.audit_context.log_step("customer_processing", "razorpay_fetch_failed", {
                                'error_code': error_code,
                                'error_description': error_desc
                            })

                            raise PaymentProcessingError(
                                f"Failed to fetch customer from Razorpay: {error_code}: {error_desc}",
                                details={'customer_id': self.razorpay_customer_id, 'error_code': error_code}
                            )

                except Exception as fetch_error:
                    raise PaymentProcessingError(
                        f"Failed to retrieve existing customer: {str(fetch_error)}",
                        details={'customer_id': self.razorpay_customer_id, 'fetch_error': str(fetch_error)}
                    )
            else:
                # Create new customer
                try:
                    self.razorpay_customer = self._create_customer()

                except Exception as creation_error:
                    raise PaymentProcessingError(
                        f"Failed to create customer: {str(creation_error)}",
                        details={'creation_error': str(creation_error)}
                    )

            # Update customer ID and company record
            try:
                customer_id = self.razorpay_customer.get('id')
                self.razorpay_customer_id = customer_id

                # Update company with customer ID
                query = {'id': self.company_id}
                update_query = {'razorpay_customer_id': customer_id}
                self.db.update(DBColls.COMPANIES, query, update_query)

                return self.razorpay_customer

            except Exception as update_error:
                raise PaymentProcessingError(
                    f"Failed to update company with customer ID: {str(update_error)}",
                    details={'update_error': str(update_error)}
                )

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("customer_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Customer processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def _create_razorpay_plan(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create Razorpay plan with enhanced validation and error handling.

        Args:
            plan: Plan data dictionary

        Returns:
            Dict[str, Any]: Created Razorpay plan data

        Raises:
            PaymentValidationError: If plan data is invalid
            PaymentProcessingError: If plan creation fails
        """
        try:
            # Log plan creation start
            self.audit_context.log_step("razorpay_plan_creation", "started", {
                'plan_id': plan.get('id'),
                'plan_name': plan.get('plan_name')
            })

            # Validate billing cycle
            billing_cycle = plan.get('billing_cycle')
            supported_cycles = [BillingCycle.DAILY, BillingCycle.WEEKLY, BillingCycle.MONTHLY, BillingCycle.ANNUALLY]

            if billing_cycle not in supported_cycles:
                supported_cycle_names = [cycle for cycle in supported_cycles]
                raise PaymentValidationError(
                    f"Unsupported billing cycle: {billing_cycle}. Supported cycles: {', '.join(supported_cycle_names)}",
                    details={'billing_cycle': str(billing_cycle), 'supported_cycles': supported_cycle_names}
                )

            # Convert billing cycle for Razorpay
            razorpay_billing_cycle = billing_cycle
            if billing_cycle == BillingCycle.ANNUALLY:
                razorpay_billing_cycle = 'yearly'

            # Compute plan amount with tax
            try:
                plan_amount = self.compute_tax(
                    taxable_amount=plan['price'], gst_details=False
                )
            except Exception as tax_error:
                raise PaymentProcessingError(
                    f"Failed to compute plan amount: {str(tax_error)}",
                    details={'plan_price': plan['price'], 'tax_error': str(tax_error)}
                )

            # Get plan schema
            try:
                plan_obj = self.razorpay.plan.get_create_plan_schema()
            except Exception as schema_error:
                raise PaymentProcessingError(
                    f"Failed to get plan schema: {str(schema_error)}",
                    details={'schema_error': str(schema_error)}
                )

            # Set plan period
            plan_obj['period'] = razorpay_billing_cycle

            # Set plan notes
            if 'notes' not in plan_obj:
                plan_obj['notes'] = {}
            plan_obj['notes']['plan_id'] = plan['id']

            # Set plan item details
            if 'item' not in plan_obj:
                plan_obj['item'] = {}

            try:
                plan_obj['item'].update({
                    'name': self._get_plan_name(plan),
                    'amount': self._process_amount(plan_amount),
                    'currency': plan.get('currency', Currency.INR),
                    'description': self._get_plan_description(plan)
                })
            except Exception as item_error:
                raise PaymentProcessingError(
                    f"Failed to set plan item details: {str(item_error)}",
                    details={'item_error': str(item_error)}
                )

            # Create addon references
            try:
                plan_obj = self._create_addon_references(plan, plan_obj)
            except Exception as addon_error:
                raise PaymentProcessingError(
                    f"Failed to create addon references: {str(addon_error)}",
                    details={'addon_error': str(addon_error)}
                )

            # Validate plan payload
            try:
                plan_data = self.utility.validate_payload(
                    CreateRazorpayPlanValidator, plan_obj
                )
            except Exception as validation_error:
                raise PaymentValidationError(
                    f"Plan payload validation failed: {str(validation_error)}",
                    details={'validation_error': str(validation_error)}
                )

            # Create plan in Razorpay
            try:
                razorpay_plan, created = self.razorpay.plan.create_plan(plan_data)
            except Exception as creation_error:
                raise PaymentProcessingError(
                    f"Razorpay plan creation failed: {str(creation_error)}",
                    details={'creation_error': str(creation_error)}
                )

            # Validate plan creation success
            if not created:
                error_info = razorpay_plan.get('error', {})
                error_code = error_info.get('code', 'unknown')
                error_desc = error_info.get('description', 'Unknown error')

                self.audit_context.log_step("razorpay_plan_creation", "razorpay_failed", {
                    'error_code': error_code,
                    'error_description': error_desc
                })

                raise PaymentProcessingError(
                    f"Razorpay plan creation failed: {error_code}: {error_desc}",
                    details={'error_code': error_code, 'error_description': error_desc}
                )

            # Validate and save plan data
            try:
                razorpay_plan = RazorpayPlanSchema(**razorpay_plan).model_dump()
                self.db.insert(DBColls.RAZORPAY_PLANS, [razorpay_plan])
                razorpay_plan.pop('_id', None)
            except Exception as save_error:
                raise PaymentProcessingError(
                    f"Failed to save plan data: {str(save_error)}",
                    details={'save_error': str(save_error)}
                )

            # Update original plan with Razorpay plan ID
            try:
                query = {'id': plan['id']}
                update_query = {'razorpay_plan_id': razorpay_plan['id']}
                self.db.update(DBColls.PLANS, query, update_query)
            except Exception as update_error:
                # Log warning but don't fail plan creation
                self.audit_context.log_step("razorpay_plan_creation", "plan_update_failed", {
                    'update_error': str(update_error)
                })
                logger.warning(f"Failed to update plan with Razorpay ID: {str(update_error)}")

            # Log successful creation
            self.audit_context.log_step("razorpay_plan_creation", "completed", {
                'plan_id': plan['id'],
                'razorpay_plan_id': razorpay_plan['id'],
                'billing_cycle': str(billing_cycle)
            })

            return razorpay_plan

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("razorpay_plan_creation", "failed", {
                'plan_id': plan.get('id'),
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Razorpay plan creation failed: {str(e)}",
                details={'plan_id': plan.get('id'), 'original_error': str(e)}
            )

    @handle_payment_exception
    def _get_or_create_current_plan(self) -> Dict[str, Any]:
        """
        Get or create current Razorpay plan with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Current plan data

        Raises:
            PaymentValidationError: If plan data is invalid
            PaymentProcessingError: If plan creation/retrieval fails
        """
        try:
            # Validate current plan exists
            if not hasattr(self, 'current_plan') or not self.current_plan:
                raise PaymentValidationError(
                    "Current plan is required but not found",
                    details={'has_current_plan': hasattr(self, 'current_plan')}
                )

            # Try to get existing Razorpay plan
            if self.razorpay_current_plan_id:
                try:
                    # First check local database
                    query = {'id': self.razorpay_current_plan_id}
                    self.razorpay_current_plan = self.db.find(
                        DBColls.RAZORPAY_PLANS, query, find_one=True
                    )

                    if self.razorpay_current_plan:
                        # Validate plan data structure
                        if not isinstance(self.razorpay_current_plan, dict):
                            raise PaymentProcessingError(
                                f"Invalid plan data format: expected dict, got {type(self.razorpay_current_plan).__name__}",
                                details={'plan_id': self.razorpay_current_plan_id}
                            )

                        return self.razorpay_current_plan

                    self.razorpay_current_plan, fetched = self.razorpay.plan.get_plans(
                        self.razorpay_current_plan_id
                    )

                    if not fetched:
                        error_info = self.razorpay_current_plan.get('error', {})
                        error_code = error_info.get('code', 'unknown')
                        error_desc = error_info.get('description', 'Unknown error')

                        self.audit_context.log_step("current_plan_processing", "razorpay_fetch_failed", {
                            'error_code': error_code,
                            'error_description': error_desc
                        })

                        raise PaymentProcessingError(
                            f"Failed to fetch current plan from Razorpay: {error_code}: {error_desc}",
                            details={'plan_id': self.razorpay_current_plan_id, 'error_code': error_code}
                        )

                    return self.razorpay_current_plan

                except Exception as fetch_error:
                    raise PaymentProcessingError(
                        f"Failed to retrieve existing current plan: {str(fetch_error)}",
                        details={'plan_id': self.razorpay_current_plan_id, 'fetch_error': str(fetch_error)}
                    )

            # Create new Razorpay plan
            try:
                self.audit_context.log_step("current_plan_processing", "creating_razorpay_plan")
                self.razorpay_current_plan = self._create_razorpay_plan(self.current_plan)

                # Validate created plan
                if not isinstance(self.razorpay_current_plan, dict):
                    raise PaymentProcessingError(
                        f"Invalid created plan format: expected dict, got {type(self.razorpay_current_plan).__name__}"
                    )

                self.razorpay_current_plan_id = self.razorpay_current_plan.get('id')
                if not self.razorpay_current_plan_id:
                    raise PaymentProcessingError(
                        "Created plan missing ID",
                        details={'plan_keys': list(self.razorpay_current_plan.keys())}
                    )

                self.audit_context.log_step("current_plan_processing", "created_razorpay_plan", {
                    'plan_id': self.razorpay_current_plan_id
                })

                return self.razorpay_current_plan

            except Exception as creation_error:
                raise PaymentProcessingError(
                    f"Failed to create current Razorpay plan: {str(creation_error)}",
                    details={'creation_error': str(creation_error)}
                )

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("current_plan_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Current plan processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def _get_or_create_new_plan(self) -> Optional[Dict[str, Any]]:
        """
        Get or create new Razorpay plan for upgrade with enhanced validation and error handling.

        Returns:
            Optional[Dict[str, Any]]: New plan data if upgrade, None if not upgrade

        Raises:
            PaymentValidationError: If plan data is invalid
            PaymentProcessingError: If plan creation/retrieval fails
        """
        try:
            # Return None if not an upgrade
            if not self.upgrade_plan:
                return None

            # Validate new plan exists
            if not hasattr(self, 'new_plan') or not self.new_plan:
                raise PaymentValidationError(
                    "New plan is required for upgrade but not found",
                    details={'has_new_plan': hasattr(self, 'new_plan')}
                )

            # Try to get existing Razorpay plan
            if self.razorpay_new_plan_id:
                try:
                    # First check local database
                    query = {'id': self.razorpay_new_plan_id}
                    self.razorpay_new_plan = self.db.find(
                        DBColls.RAZORPAY_PLANS, query, find_one=True
                    )

                    if self.razorpay_new_plan:
                        # Validate plan data structure
                        if not isinstance(self.razorpay_new_plan, dict):
                            raise PaymentProcessingError(
                                f"Invalid plan data format: expected dict, got {type(self.razorpay_new_plan).__name__}",
                                details={'plan_id': self.razorpay_new_plan_id}
                            )

                        return self.razorpay_new_plan

                    self.razorpay_new_plan, fetched = self.razorpay.plan.get_plans(
                        self.razorpay_new_plan_id
                    )

                    if not fetched:
                        error_info = self.razorpay_new_plan.get('error', {})
                        error_code = error_info.get('code', 'unknown')
                        error_desc = error_info.get('description', 'Unknown error')

                        self.audit_context.log_step("new_plan_processing", "razorpay_fetch_failed", {
                            'error_code': error_code,
                            'error_description': error_desc
                        })

                        raise PaymentProcessingError(
                            f"Failed to fetch new plan from Razorpay: {error_code}: {error_desc}",
                            details={'plan_id': self.razorpay_new_plan_id, 'error_code': error_code}
                        )

                    return self.razorpay_new_plan

                except Exception as fetch_error:
                    raise PaymentProcessingError(
                        f"Failed to retrieve existing new plan: {str(fetch_error)}",
                        details={'plan_id': self.razorpay_new_plan_id, 'fetch_error': str(fetch_error)}
                    )

            # Create new Razorpay plan
            try:
                self.razorpay_new_plan = self._create_razorpay_plan(self.new_plan)

                self.razorpay_new_plan_id = self.razorpay_new_plan.get('id')
                if not self.razorpay_new_plan_id:
                    raise PaymentProcessingError(
                        "Created plan missing ID",
                        details={'plan_keys': list(self.razorpay_new_plan.keys())}
                    )

                return self.razorpay_new_plan

            except Exception as creation_error:
                raise PaymentProcessingError(
                    f"Failed to create new Razorpay plan: {str(creation_error)}",
                    details={'creation_error': str(creation_error)}
                )

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("new_plan_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"New plan processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def subscribe(self) -> Dict[str, Any]:
        """
        Create subscription with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Subscription creation result

        Raises:
            PaymentValidationError: If subscription data is invalid
            PaymentProcessingError: If subscription creation fails
        """
        try:
            # Log subscription creation start
            self.audit_context.log_step("subscription_creation", "started", {
                'current_plan_id': getattr(self, 'current_plan', {}).get('id'),
                'current_subscription_id': getattr(self, 'current_subscription', {}).get('id')
            })

            # Validate required data exists
            if not hasattr(self, 'current_plan') or not self.current_plan:
                raise PaymentValidationError(
                    "Current plan is required for subscription creation",
                    details={'has_current_plan': hasattr(self, 'current_plan')}
                )

            if not hasattr(self, 'current_subscription') or not self.current_subscription:
                raise PaymentValidationError(
                    "Current subscription is required for subscription creation",
                    details={'has_current_subscription': hasattr(self, 'current_subscription')}
                )

            # Get utility class
            try:
                utility_cls = self._get_utility()
            except Exception as utility_error:
                raise PaymentProcessingError(
                    f"Failed to get utility class: {str(utility_error)}",
                    details={'utility_error': str(utility_error)}
                )

            # Create utility instance
            try:
                utility_inst = utility_cls(self)
            except Exception as instance_error:
                raise PaymentProcessingError(
                    f"Failed to create utility instance: {str(instance_error)}",
                    details={'instance_error': str(instance_error)}
                )

            # Create subscription
            try:
                self.audit_context.log_step("subscription_creation", "creating_subscription")
                payments_data = utility_inst.create_subscription(
                    self.current_plan, self.current_subscription
                )
            except Exception as creation_error:
                raise PaymentProcessingError(
                    f"Failed to create subscription: {str(creation_error)}",
                    details={'creation_error': str(creation_error)}
                )

            # Fetch and create invoices
            try:
                self.audit_context.log_step("subscription_creation", "fetching_invoices")
                utility_inst.fetch_and_create_invoices()
            except Exception as invoice_error:
                # Log warning but don't fail subscription creation
                self.audit_context.log_step("subscription_creation", "invoice_fetch_failed", {
                    'invoice_error': str(invoice_error)
                })
                logger.warning(f"Failed to fetch invoices for subscription: {str(invoice_error)}")

            # Set payments data on utility
            self.utility.payments_data = payments_data

            # Log successful creation
            self.audit_context.log_step("subscription_creation", "completed", {
                'payments_data_keys': list(payments_data.keys()) if payments_data else [],
                'has_payments_data': bool(payments_data)
            })

            return payments_data

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("subscription_creation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Subscription creation failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def upgrade(self) -> Dict[str, Any]:
        """
        Upgrade subscription with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: Subscription upgrade result

        Raises:
            PaymentValidationError: If upgrade data is invalid
            PaymentProcessingError: If subscription upgrade fails
        """
        try:
            # Log subscription upgrade start
            self.audit_context.log_step("subscription_upgrade", "started", {
                'new_plan_id': getattr(self, 'new_plan', {}).get('id'),
                'current_subscription_id': getattr(self, 'current_subscription', {}).get('id')
            })

            # Validate required data exists
            if not hasattr(self, 'new_plan') or not self.new_plan:
                raise PaymentValidationError(
                    "New plan is required for subscription upgrade",
                    details={'has_new_plan': hasattr(self, 'new_plan')}
                )

            if not hasattr(self, 'current_subscription') or not self.current_subscription:
                raise PaymentValidationError(
                    "Current subscription is required for subscription upgrade",
                    details={'has_current_subscription': hasattr(self, 'current_subscription')}
                )

            # Validate upgrade is actually an upgrade (not downgrade)
            current_plan_price = getattr(self, 'current_plan', {}).get('price', 0)
            new_plan_price = self.new_plan.get('price', 0)

            if new_plan_price <= current_plan_price:
                self.audit_context.log_step("subscription_upgrade", "price_validation_warning", {
                    'current_plan_price': current_plan_price,
                    'new_plan_price': new_plan_price
                })
                logger.warning(f"Upgrade to lower/same price plan: {current_plan_price} -> {new_plan_price}")

            # Get utility class
            try:
                utility_cls = self._get_utility()
            except Exception as utility_error:
                raise PaymentProcessingError(
                    f"Failed to get utility class for upgrade: {str(utility_error)}",
                    details={'utility_error': str(utility_error)}
                )

            # Create utility instance
            try:
                utility_inst = utility_cls(self)
            except Exception as instance_error:
                raise PaymentProcessingError(
                    f"Failed to create utility instance for upgrade: {str(instance_error)}",
                    details={'instance_error': str(instance_error)}
                )

            # Upgrade subscription
            try:
                self.audit_context.log_step("subscription_upgrade", "upgrading_subscription")
                payments_data = utility_inst.upgrade_subscription(
                    self.new_plan, self.current_subscription
                )
            except Exception as upgrade_error:
                raise PaymentProcessingError(
                    f"Failed to upgrade subscription: {str(upgrade_error)}",
                    details={'upgrade_error': str(upgrade_error)}
                )

            # Fetch and create invoices
            try:
                self.audit_context.log_step("subscription_upgrade", "fetching_invoices")
                utility_inst.fetch_and_create_invoices()
            except Exception as invoice_error:
                # Log warning but don't fail upgrade
                self.audit_context.log_step("subscription_upgrade", "invoice_fetch_failed", {
                    'invoice_error': str(invoice_error)
                })
                logger.warning(f"Failed to fetch invoices for upgrade: {str(invoice_error)}")

            # Set payments data on utility
            self.utility.payments_data = payments_data

            # Log successful upgrade
            self.audit_context.log_step("subscription_upgrade", "completed", {
                'payments_data_keys': list(payments_data.keys()) if payments_data else [],
                'has_payments_data': bool(payments_data),
                'new_plan_id': self.new_plan.get('id')
            })

            return payments_data

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("subscription_upgrade", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Subscription upgrade failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def payg(self) -> Dict[str, Any]:
        """
        Process PAYG (Pay-as-you-go) payment with enhanced validation and error handling.

        Returns:
            Dict[str, Any]: PAYG payment processing result

        Raises:
            PaymentValidationError: If PAYG data is invalid
            PaymentProcessingError: If PAYG processing fails
        """
        try:
            # Log PAYG processing start
            self.audit_context.log_step("payg_processing", "started", {
                'company_id': self.company_id,
                'subscription_id': self.current_subscription_id
            })

            # Validate PAYG payload exists
            if not hasattr(self, 'payload') or not self.payload:
                raise PaymentValidationError(
                    "Payload is required for PAYG processing",
                    details={'has_payload': hasattr(self, 'payload')}
                )

            # Create PAYG utility instance
            try:
                utility_inst = PaygUtils(self)
            except Exception as instance_error:
                raise PaymentProcessingError(
                    f"Failed to create PAYG utility instance: {str(instance_error)}",
                    details={'instance_error': str(instance_error)}
                )

            # Create PAYG order
            try:
                self.audit_context.log_step("payg_processing", "creating_order")
                payments_data = utility_inst.create_order()
            except Exception as order_error:
                raise PaymentProcessingError(
                    f"Failed to create PAYG order: {str(order_error)}",
                    details={'order_error': str(order_error)}
                )

            # Set payments data on utility
            self.utility.payments_data = payments_data

            # Log successful PAYG processing
            self.audit_context.log_step("payg_processing", "completed", {
                'payments_data_keys': list(payments_data.keys()) if payments_data else [],
                'has_payments_data': bool(payments_data)
            })

            return payments_data

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("payg_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"PAYG processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    @with_resilience(
        circuit_breaker_name="subscription_processing",
        max_retries=3,
        failure_threshold=5,
        recovery_timeout=60
    )
    def process_subscriptions(self) -> Dict[str, Any]:
        """
        Process subscription payments with enhanced error handling and resilience.

        Returns:
            Dict: Subscription processing result

        Raises:
            PaymentValidationError: If event type is invalid
            PaymentBusinessLogicError: If business rules are violated
            PaymentProcessingError: If processing fails
        """
        try:
            # Log subscription processing start
            self.audit_context.log_step("subscription_processing", "started", {
                'event': self.event,
                'company_id': self.company_id,
                'subscription_id': self.current_subscription_id
            })

            # Validate event type
            if self.event not in [SubscriptionEvent.SUBSCRIBE, SubscriptionEvent.UPGRADE]:
                raise PaymentValidationError(
                    f'Invalid subscription event: {self.event}. Only SUBSCRIBE and UPGRADE are supported.',
                    details={'provided_event': self.event}
                )

            # Handle test live mode
            if self.razorpay.test_live_mode:
                self.audit_context.log_step("test_live_mode", "started")
                result = self._simulate_live_mode()
                self.audit_context.log_step("test_live_mode", "completed")
                return result

            # Execute subscription processing steps
            self._execute_subscription_processing_steps()

            # Determine processing path
            if self.upgrade_plan:
                self.audit_context.log_step("subscription_upgrade", "started")
                result = self.upgrade()
                self.audit_context.log_step("subscription_upgrade", "completed")
            else:
                self.audit_context.log_step("subscription_creation", "started")
                result = self.subscribe()
                self.audit_context.log_step("subscription_creation", "completed")

            # Log successful completion
            self.audit_context.log_step("subscription_processing", "completed", {
                'processing_type': 'upgrade' if self.upgrade_plan else 'subscribe'
            })

            return result

        except Exception as e:
            # Log processing failure
            self.audit_context.log_step("subscription_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })
            raise

    def _execute_subscription_processing_steps(self):
        """Execute the core subscription processing steps with validation."""
        steps = [
            ("get_company", self._get_company),
            ("validate_subscription", self._check_if_valid_subscription),
            ("validate_plan", self._check_if_valid_plan),
            ("get_or_create_current_plan", self._get_or_create_current_plan),
            ("get_or_create_new_plan", self._get_or_create_new_plan)
        ]

        for step_name, step_func in steps:
            try:
                self.audit_context.log_step(step_name, "started")
                step_func()
                self.audit_context.log_step(step_name, "completed")
            except Exception as e:
                self.audit_context.log_step(step_name, "failed", {
                    'error': str(e)
                })
                raise PaymentProcessingError(
                    f"Failed during {step_name}: {str(e)}",
                    details={'step': step_name, 'original_error': str(e)}
                )

    @handle_payment_exception
    @with_resilience(
        circuit_breaker_name="payg_processing",
        max_retries=3,
        failure_threshold=5,
        recovery_timeout=60
    )
    def process_payg(self) -> Dict[str, Any]:
        """
        Process PAYG payments with enhanced error handling and resilience.

        Returns:
            Dict: PAYG processing result

        Raises:
            PaymentValidationError: If not a PAYG event
            PaymentProcessingError: If processing fails
        """
        try:
            # Log PAYG processing start
            self.audit_context.log_step("payg_processing", "started", {
                'event': self.event,
                'company_id': self.company_id,
                'subscription_id': self.current_subscription_id
            })

            # Validate PAYG event
            if not self.is_payg:
                raise PaymentValidationError(
                    f'Invalid event for PAYG processing: {self.event}. Expected PAYG event.',
                    details={'provided_event': self.event}
                )

            # Execute PAYG processing steps
            self._execute_payg_processing_steps()

            # Process PAYG
            self.audit_context.log_step("payg_order_creation", "started")
            result = self.payg()
            self.audit_context.log_step("payg_order_creation", "completed")

            # Log successful completion
            self.audit_context.log_step("payg_processing", "completed", {})

            return result

        except Exception as e:
            # Log processing failure
            self.audit_context.log_step("payg_processing", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })
            raise

    def _execute_payg_processing_steps(self):
        """Execute the core PAYG processing steps with validation."""
        steps = [
            ("get_company", self._get_company),
            ("validate_subscription", self._check_if_valid_subscription),
            ("validate_plan", self._check_if_valid_plan)
        ]

        for step_name, step_func in steps:
            try:
                self.audit_context.log_step(step_name, "started")
                step_func()
                self.audit_context.log_step(step_name, "completed")
            except Exception as e:
                self.audit_context.log_step(step_name, "failed", {
                    'error': str(e)
                })
                raise PaymentProcessingError(
                    f"Failed during {step_name}: {str(e)}",
                    details={'step': step_name, 'original_error': str(e)}
                )

    @handle_payment_exception
    def create_order_invoice(self, order: Dict[str, Any], payment: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create order invoice with enhanced validation and error handling.

        Args:
            order: Order data dictionary
            payment: Payment data dictionary

        Returns:
            Dict[str, Any]: Created invoice data

        Raises:
            PaymentValidationError: If order or payment data is invalid
            PaymentProcessingError: If invoice creation fails
        """
        try:
            # Log invoice creation start
            self.audit_context.log_step("order_invoice_creation", "started")

            # Ensure company data is available
            try:
                self._get_company()
            except Exception as company_error:
                raise PaymentProcessingError(
                    f"Failed to get company data for invoice: {str(company_error)}",
                    details={'company_error': str(company_error)}
                )

            order_amount = order.get('amount')
            order_id = order.get('id')
            if not order_id:
                raise PaymentValidationError("Order ID missing")

            payment_amount = payment.get('amount')
            payment_id = payment.get('id')
            if not payment_id:
                raise PaymentValidationError("Payment missing ID")

            # Calculate amounts
            amount_due = payment_amount - order_amount
            amount_in_rupees = payment_amount / 100

            # Get order notes
            notes = order.get('notes', {})

            # Create invoice schema
            try:
                invoice = RazorpayInvoiceSchema().model_dump()
            except Exception as schema_error:
                raise PaymentProcessingError(
                    f"Failed to create invoice schema: {str(schema_error)}",
                    details={'schema_error': str(schema_error)}
                )

            # Generate invoice number
            try:
                invoice_number = generate_sequence_no(SequenceType.INVOICE_NUMBER)
            except Exception as sequence_error:
                raise PaymentProcessingError(
                    f"Failed to generate invoice number: {str(sequence_error)}",
                    details={'sequence_error': str(sequence_error)}
                )

            # Set invoice basic details
            invoice.update({
                'order_id': order_id,
                'receipt': order.get('receipt'),
                'order_no': order.get('order_no'),
                'line_items': order.get('items', []),
                'invoice_number': invoice_number,
                'subscription_id': notes.get('subscription_id'),
                'description': payment.get('description'),
                'customer_id': self.company.get('id'),
                'issued_at': order.get('created_at'),
                'paid_at': payment.get('created_at'),
                'amount_in_rupees': amount_in_rupees,
                'created_at': (self.now // 1000),
                'date': order.get('created_at'),
                'status': order.get('status'),
                'payment_id': payment_id,
                'amount_paid': payment_amount,
                'amount_due': amount_due,
                'gross_amount': order_amount,
                'amount': order_amount,
                'notes': notes
            })

            # Set customer details
            if 'customer_details' not in invoice:
                invoice['customer_details'] = {}

            invoice['customer_details'].update({
                'customer_name': getattr(self.request, 'company_name', ''),
                'shipping_address': self.company.get('address'),
                'billing_address': self.company.get('address'),
                'name': getattr(self.request, 'user_name', ''),
                'customer_contact': self.company.get('phone'),
                'customer_email': self.company.get('email'),
                'email': getattr(self.request, 'email', ''),
                'contact': self.company.get('phone'),
                'gstin': self.company.get('gstin'),
                'id': self.company.get('id')
            })

            # Compute tax details
            try:
                tax_details = back_compute_tax(amount_in_rupees)
                invoice['amount_in_words'] = number_to_words(amount_in_rupees)
                tax_details = compute_gst_details(tax_details, self.company.get('state_code'))
                invoice.update(tax_details)
            except Exception as tax_error:
                raise PaymentProcessingError(
                    f"Failed to compute tax details: {str(tax_error)}",
                    details={'tax_error': str(tax_error)}
                )

            self.db.insert(DBColls.RAZORPAY_INVOICES, [invoice])
            invoice.pop('_id', None)

            # Log successful creation
            self.audit_context.log_step("order_invoice_creation", "completed")
            return invoice

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("order_invoice_creation", "failed", {
                'order_id': order.get('id') if isinstance(order, dict) else None,
                'payment_id': payment.get('id') if isinstance(payment, dict) else None,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Order invoice creation failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def update_order_invoice(self, notes: Dict[str, Any], order: Dict[str, Any], invoice_id: str) -> Dict[str, Any]:
        """
        Update order invoice with enhanced validation and error handling.

        Args:
            notes: Invoice notes dictionary
            order: Order data dictionary
            invoice_id: Invoice ID to update

        Returns:
            Dict[str, Any]: Updated invoice data

        Raises:
            PaymentValidationError: If invoice data is invalid
            PaymentProcessingError: If invoice update fails
        """
        try:
            # Log invoice update start
            self.audit_context.log_step("order_invoice_update", "started")

            # Validate invoice ID
            if not invoice_id:
                raise PaymentValidationError(
                    "Invoice ID is required for update",
                    details={'invoice_id': invoice_id}
                )

            # Ensure company data is available
            try:
                self._get_company()
            except Exception as company_error:
                raise PaymentProcessingError(
                    f"Failed to get company data for invoice update: {str(company_error)}",
                    details={'company_error': str(company_error)}
                )

            # Fetch invoice from Razorpay
            try:
                invoice, fetched = self.razorpay.invoice.get_invoices(invoice_id=invoice_id)
            except Exception as fetch_error:
                raise PaymentProcessingError(
                    f"Failed to fetch invoice from Razorpay: {str(fetch_error)}",
                    details={'invoice_id': invoice_id, 'fetch_error': str(fetch_error)}
                )

            # Validate invoice fetch success
            if not fetched:
                error_info = invoice.get('error', {})
                error_code = error_info.get('code', 'unknown')
                error_desc = error_info.get('description', 'Unknown error')

                raise PaymentProcessingError(
                    f"Unable to retrieve invoice details: {error_code}: {error_desc}",
                    details={'invoice_id': invoice_id, 'error_code': error_code}
                )

            # Calculate amount in rupees
            invoice_amount = invoice.get('amount')
            amount_in_rupees = invoice_amount / 100

            # Compute tax details
            try:
                update_query = back_compute_tax(amount_in_rupees)
                update_query = compute_gst_details(update_query, self.company.get('state_code'))
            except Exception as tax_error:
                raise PaymentProcessingError(
                    f"Failed to compute tax details: {str(tax_error)}",
                    details={'tax_error': str(tax_error)}
                )

            # Generate amount in words
            try:
                amount_in_words = number_to_words(amount_in_rupees)
            except Exception as words_error:
                raise PaymentProcessingError(
                    f"Failed to generate amount in words: {str(words_error)}",
                    details={'words_error': str(words_error)}
                )

            # Update query with basic details
            update_query.update({
                'amount_in_words': amount_in_words,
                'customer_id': self.company.get('id'),
                'amount_in_rupees': amount_in_rupees,
                'order_no': order.get('order_no'),
                'receipt': order.get('receipt'),
                'notes': notes
            })

            # Set customer details
            customer_details = {
                'customer_name': getattr(self.request, 'company_name', ''),
                'shipping_address': self.company.get('address'),
                'billing_address': self.company.get('address'),
                'name': getattr(self.request, 'user_name', ''),
                'customer_contact': self.company.get('phone'),
                'customer_email': self.company.get('email'),
                'email': getattr(self.request, 'email', ''),
                'contact': self.company.get('phone'),
                'gstin': self.company.get('gstin'),
                'id': self.company.get('id')
            }
            update_query['customer_details'] = customer_details
            invoice.update(update_query)

            # Check if invoice exists in database
            try:
                existing_invoice = self.db.find(DBColls.RAZORPAY_INVOICES, {'id': invoice_id}, find_one=True)
            except Exception as db_error:
                raise PaymentProcessingError(
                    f"Failed to check existing invoice: {str(db_error)}",
                    details={'db_error': str(db_error)}
                )

            if not existing_invoice:
                # Create new invoice
                try:
                    invoice['invoice_number'] = generate_sequence_no(SequenceType.INVOICE_NUMBER)
                    self.db.insert(DBColls.RAZORPAY_INVOICES, [invoice])
                    invoice.pop('_id', None)

                except Exception as create_error:
                    raise PaymentProcessingError(
                        f"Failed to create new invoice: {str(create_error)}",
                        details={'create_error': str(create_error)}
                    )
            else:
                # Update existing invoice
                try:
                    finder = Finder()
                    finder.track_changes(invoice, existing_invoice)
                    final_update_query = copy.deepcopy(finder._get_update_query())

                    # Generate invoice number if missing
                    if not existing_invoice.get('invoice_number'):
                        final_update_query['invoice_number'] = generate_sequence_no(SequenceType.INVOICE_NUMBER)

                    if final_update_query:
                        invoice = self.db.update(
                            DBColls.RAZORPAY_INVOICES,
                            {'id': invoice_id},
                            final_update_query,
                            find_one_and_update=True
                        )

                except Exception as update_error:
                    raise PaymentProcessingError(
                        f"Failed to update existing invoice: {str(update_error)}",
                        details={'update_error': str(update_error)}
                    )

            # Log successful update
            self.audit_context.log_step("order_invoice_update", "completed")
            return invoice

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("order_invoice_update", "failed", {
                'invoice_id': invoice_id,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Order invoice update failed: {str(e)}",
                details={'invoice_id': invoice_id, 'original_error': str(e)}
            )

    @handle_payment_exception
    def update_subscription(self, notes: Dict[str, Any], rzp_sub: Dict[str, Any]) -> None:
        """
        Update subscription with enhanced validation and error handling.

        Args:
            notes: Subscription notes dictionary
            rzp_sub: Razorpay subscription data

        Raises:
            PaymentValidationError: If subscription data is invalid
            PaymentProcessingError: If subscription update fails
        """
        try:
            # Log subscription update start
            self.audit_context.log_step("subscription_update", "started")

            # Ensure company data is available
            try:
                self._get_company()
            except Exception as company_error:
                raise PaymentProcessingError(
                    f"Failed to get company data for subscription update: {str(company_error)}",
                    details={'company_error': str(company_error)}
                )

            # Extract and validate required fields
            event = notes.get('event')
            subscription_id = notes.get('subscription_id')

            if not subscription_id:
                raise PaymentValidationError(
                    "Subscription ID is required in notes",
                    details={'notes_keys': list(notes.keys())}
                )

            if not event:
                raise PaymentValidationError(
                    "Event is required in notes",
                    details={'notes_keys': list(notes.keys())}
                )

            # Get subscription from database
            try:
                subscription = self.db.find(DBColls.SUBSCRIPTIONS, {'id': subscription_id}, find_one=True)
            except Exception as db_error:
                raise PaymentProcessingError(
                    f"Failed to fetch subscription: {str(db_error)}",
                    details={'subscription_id': subscription_id, 'db_error': str(db_error)}
                )

            if not subscription:
                raise PaymentValidationError(
                    f"Subscription not found: {subscription_id}",
                    details={'subscription_id': subscription_id}
                )

            # Extract subscription details
            product_id = subscription.get('product_id')
            if not product_id:
                raise PaymentValidationError("Subscription missing product_id")

            product_id_str = str(product_id)
            is_subscribe = (event == SubscriptionEvent.SUBSCRIBE)
            is_trial_used = self.company.get('is_trial_used', {})

            if not isinstance(is_trial_used, dict):
                is_trial_used = {}

            product_trial_used = is_trial_used.get(product_id_str, False)
            is_pending = (subscription.get('status_id') == SubscriptionStatus.PENDING.value)

            # Initialize update queries
            update_query = {}
            company_update_query = {}

            # Check trial conditions
            trial_enabled = self.razorpay.saas_settings.get('trial_period_enabled', False)
            should_start_trial = all([is_subscribe, is_pending, trial_enabled]) and not product_trial_used

            if should_start_trial:
                # Set trial subscription
                try:
                    update_query.update({
                        'start_date': self.now,
                        'is_authenticated': True,
                        'end_date': convert_to_ms(rzp_sub['start_at']),
                        'status_id': SubscriptionStatus.TRIAL.value,
                        'status': SubscriptionStatus.TRIAL.name,
                    })

                    trial_key = f'is_trial_used.{product_id_str}'
                    company_update_query = {trial_key: True}

                except Exception as trial_error:
                    raise PaymentProcessingError(
                        f"Failed to set trial subscription: {str(trial_error)}",
                        details={'trial_error': str(trial_error)}
                    )
            else:
                # Set active subscription
                try:
                    update_query.update({
                        'start_date': convert_to_ms(rzp_sub['start_at']),
                        'end_date': convert_to_ms(rzp_sub['end_at']),
                        'status_id': SubscriptionStatus.ACTIVE.value,
                        'status': SubscriptionStatus.ACTIVE.name
                    })

                    # Reset lanes on subscription charged
                    plan = self.db.find(DBColls.PLANS, {'id': subscription['plan_id']}, find_one=True)

                    if plan and isinstance(plan, dict):
                        features = plan.get('features', [])
                        if isinstance(features, list):
                            for feature in features:
                                feature_id = feature.get('feature_id')
                                if feature_id == UsageKeys.NUMBER_OF_LANES.value:
                                    lanes_value = feature.get('ival')
                                    update_query.update({
                                        'usage.remaining_plan_lanes': lanes_value,
                                        'usage.plan_lanes': lanes_value
                                    })
                                    break

                except Exception as active_error:
                    raise PaymentProcessingError(
                        f"Failed to set active subscription: {str(active_error)}",
                        details={'active_error': str(active_error)}
                    )

            # Update subscription in database
            if update_query:
                self.db.update(DBColls.SUBSCRIPTIONS, {'id': subscription_id}, update_query)

            # Update company trial status
            if company_update_query:
                self.db.update(DBColls.COMPANIES, {'id': self.company_id}, company_update_query)

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("subscription_update", "failed", {
                'subscription_id': notes.get('subscription_id') if isinstance(notes, dict) else None,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Subscription update failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def update_subscription_order(self, notes: Dict[str, Any], order_id: str) -> Dict[str, Any]:
        """
        Update subscription order with enhanced validation and error handling.

        Args:
            notes: Order notes dictionary
            order_id: Order ID to update

        Returns:
            Dict[str, Any]: Updated order data

        Raises:
            PaymentValidationError: If order data is invalid
            PaymentProcessingError: If order update fails
        """
        try:
            # Log order update start
            self.audit_context.log_step("subscription_order_update", "started")

            # Validate order ID
            if not order_id:
                raise PaymentValidationError(
                    "Order ID is required for update",
                    details={'order_id': order_id}
                )

            # Fetch order from Razorpay
            try:
                order, fetched = self.razorpay.order.get_orders(order_id=order_id)
            except Exception as fetch_error:
                raise PaymentProcessingError(
                    f"Failed to fetch order from Razorpay: {str(fetch_error)}",
                    details={'order_id': order_id, 'fetch_error': str(fetch_error)}
                )

            # Validate order fetch success
            if not fetched:
                error_info = order.get('error', {})
                error_code = error_info.get('code', 'unknown')
                error_desc = error_info.get('description', 'Unknown error')

                raise PaymentProcessingError(
                    f"Unable to retrieve order details: {error_code}: {error_desc}",
                    details={'order_id': order_id, 'error_code': error_code}
                )

            # Update order with notes
            order['notes'] = notes

            # Check if order exists in database
            existing_order = self.db.find(DBColls.RAZORPAY_ORDERS, {'id': order_id}, find_one=True)

            if not existing_order:
                # Create new order
                try:
                    order['order_no'] = self.now
                    order['receipt'] = generate_sequence_no(SequenceType.RECEIPT_NUMBER)
                    self.db.insert(DBColls.RAZORPAY_ORDERS, [order])
                    order.pop('_id', None)

                except Exception as create_error:
                    raise PaymentProcessingError(
                        f"Failed to create new order: {str(create_error)}",
                        details={'order_id': order_id, 'create_error': str(create_error)}
                    )
            else:
                # Update existing order
                try:
                    finder = Finder()
                    finder.track_changes(order, existing_order)
                    update_query = copy.deepcopy(finder._get_update_query())

                    if update_query:
                        order = self.db.update(DBColls.RAZORPAY_ORDERS, {'id': order_id}, update_query, find_one_and_update=True)

                except Exception as update_error:
                    raise PaymentProcessingError(
                        f"Failed to update existing order: {str(update_error)}",
                        details={'order_id': order_id, 'update_error': str(update_error)}
                    )

            return order

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("subscription_order_update", "failed", {
                'order_id': order_id,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Subscription order update failed: {str(e)}",
                details={'order_id': order_id, 'original_error': str(e)}
            )

    @handle_payment_exception
    def update_payments(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update payments with enhanced validation and error handling.

        Args:
            payload: Payment payload dictionary

        Returns:
            Dict[str, Any]: Updated payment data

        Raises:
            PaymentValidationError: If payment data is invalid
            PaymentProcessingError: If payment update fails
        """
        try:
            # Log payment update start
            self.audit_context.log_step("payment_update", "started")

            # Extract and validate payment ID
            payment_id = payload.get('razorpay_payment_id')
            if not payment_id:
                raise PaymentValidationError(
                    "Payment ID is required in payload",
                    details={'payload_keys': list(payload.keys())}
                )

            # Fetch payment from Razorpay
            try:
                payment, fetched = self.razorpay.payment.get_payments(pay_id=payment_id)
            except Exception as fetch_error:
                raise PaymentProcessingError(
                    f"Failed to fetch payment from Razorpay: {str(fetch_error)}",
                    details={'payment_id': payment_id, 'fetch_error': str(fetch_error)}
                )

            # Validate payment fetch success
            if not fetched:
                error_info = payment.get('error', {})
                error_code = error_info.get('code', 'unknown')
                error_desc = error_info.get('description', 'Unknown error')

                raise PaymentProcessingError(
                    f"Unable to retrieve payment details: {error_code}: {error_desc}",
                    details={'payment_id': payment_id, 'error_code': error_code}
                )

            # Handle subscription-related payment
            rzp_sub_id = payload.get('razorpay_subscription_id')
            if rzp_sub_id:
                try:
                    # Get Razorpay subscription
                    rzp_sub = self.db.find(DBColls.RAZORPAY_SUBSCRIPTIONS, {'id': rzp_sub_id}, find_one=True)

                    if not rzp_sub:
                        raise PaymentValidationError(
                            f"Razorpay subscription not found: {rzp_sub_id}",
                            details={'subscription_id': rzp_sub_id}
                        )

                    # Get subscription notes
                    rzp_notes = rzp_sub.get('notes', {})
                    if not isinstance(rzp_notes, dict):
                        rzp_notes = {}

                    # Update subscription
                    self.update_subscription(rzp_notes, rzp_sub)

                    # Handle order if present
                    order_id = payment.get('order_id')
                    if order_id:
                        order = self.update_subscription_order(rzp_notes, order_id)

                        # Handle invoice
                        invoice_id = payment.get('invoice_id')
                        if invoice_id:
                            invoice = self.update_order_invoice(rzp_notes, order, invoice_id)
                        else:
                            invoice = self.create_order_invoice(order, payment)

                        # Update payment with invoice details
                        if isinstance(invoice, dict):
                            payment['invoice_id'] = invoice.get('id')
                            payment['invoice_number'] = invoice.get('invoice_number')

                except Exception as subscription_error:
                    raise PaymentProcessingError(
                        f"Failed to process subscription payment: {str(subscription_error)}",
                        details={'subscription_id': rzp_sub_id, 'subscription_error': str(subscription_error)}
                    )

            # Update payment in database
            try:
                existing_payment = self.db.find(DBColls.RAZORPAY_PAYMENTS, {'id': payment_id}, find_one=True)

                if not existing_payment:
                    # Create new payment
                    self.db.insert(DBColls.RAZORPAY_PAYMENTS, [payment])
                    payment.pop('_id', None)
                else:
                    finder = Finder()
                    finder.track_changes(payment, existing_payment)
                    update_query = copy.deepcopy(finder._get_update_query())

                    if update_query:
                        payment = self.db.update(DBColls.RAZORPAY_PAYMENTS, {'id': payment_id}, update_query, find_one_and_update=True)

            except Exception as db_error:
                raise PaymentProcessingError(
                    f"Failed to update payment in database: {str(db_error)}",
                    details={'payment_id': payment_id, 'db_error': str(db_error)}
                )

            # Validate payment status
            payment_status = payment.get('status')
            valid_statuses = [RazorpayPaymentStatus.CAPTURED, RazorpayPaymentStatus.REFUNDED]

            if payment_status not in valid_statuses:
                raise PaymentValidationError(
                    "Payment is being verified & will be processed shortly",
                    details={'payment_id': payment_id, 'status': payment_status}
                )

            return payment

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("payment_update", "failed", {
                'payment_id': payload.get('razorpay_payment_id') if isinstance(payload, dict) else None,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Payment update failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def update_order_payments(self, order: Dict[str, Any], payload: Dict[str, Any]) -> None:
        """
        Update order payments with enhanced validation and error handling.

        Args:
            order: Order data dictionary
            payload: Payment payload dictionary

        Raises:
            PaymentValidationError: If order or payment data is invalid
            PaymentProcessingError: If order payment update fails
        """
        try:
            # Log order payment update start
            self.audit_context.log_step("order_payment_update", "started")

            # Get order notes
            notes = order.get('notes', {})
            if not isinstance(notes, dict):
                notes = {}

            # Update payment
            try:
                payment = self.update_payments(payload)
            except Exception as payment_error:
                raise PaymentProcessingError(
                    f"Failed to update payment: {str(payment_error)}",
                    details={'payment_error': str(payment_error)}
                )

            # Handle subscription-related updates
            subscription_id = notes.get('subscription_id')
            if subscription_id:
                try:
                    inc_query = {}
                    items = order.get('items', [])

                    # Process order items for lane updates
                    for i, item in enumerate(items):
                        quantity = item.get('quantity')
                        inc_query.update({
                            'usage.purchased_lanes': quantity,
                            'usage.remaining_purchased_lanes': quantity
                        })

                    # Update subscription usage
                    if inc_query:
                        self.db.update(DBColls.SUBSCRIPTIONS, {'id': subscription_id}, inc_query=inc_query)

                except Exception as subscription_error:
                    raise PaymentProcessingError(
                        f"Failed to update subscription usage: {str(subscription_error)}",
                        details={'subscription_id': subscription_id, 'subscription_error': str(subscription_error)}
                    )

            # Update order status
            try:
                order_id = payload.get('razorpay_order_id')
                if not order_id:
                    raise PaymentValidationError(
                        "Order ID is required in payload",
                        details={'payload_keys': list(payload.keys())}
                    )

                update_query = {'status': OrderStatus.PAID}
                order = self.db.update(DBColls.RAZORPAY_ORDERS, {'id': order_id}, update_query, find_one_and_update=True
                )

            except Exception as order_error:
                raise PaymentProcessingError(
                    f"Failed to update order status: {str(order_error)}",
                    details={'order_error': str(order_error)}
                )

            # Create invoice
            try:
                invoice = self.create_order_invoice(order, payment)
            except Exception as invoice_error:
                raise PaymentProcessingError(
                    f"Failed to create order invoice: {str(invoice_error)}",
                    details={'invoice_error': str(invoice_error)}
                )

            # Update payment with invoice details
            try:
                pay_update_query = {}
                pay_query = {'id': payment['id']}

                if not payment.get('invoice_id'):
                    pay_update_query['invoice_id'] = invoice.get('id')

                if not payment.get('invoice_number'):
                    pay_update_query['invoice_number'] = invoice.get('invoice_number')

                if pay_update_query:
                    payment = self.db.update(DBColls.RAZORPAY_PAYMENTS, pay_query, pay_update_query, find_one_and_update=True)

            except Exception as payment_update_error:
                raise PaymentProcessingError(
                    f"Failed to update payment with invoice details: {str(payment_update_error)}",
                    details={'payment_update_error': str(payment_update_error)}
                )

            # Send invoice email
            try:
                email_body = "order_paid.html"
                email_subject = f"Successfully purchased {invoice.get('description', 'Order')} at SCLEN.AI"
                email_body_context = {
                    'description': invoice.get('description'),
                    'price': invoice.get('amount_in_rupees'),
                    'line_items': invoice.get('line_items', []),
                    'created_at': invoice.get('created_at'),
                    'order_no': invoice.get('order_no'),
                    'pay_id': invoice.get('payment_id'),
                    'paid_at': invoice.get('paid_at')
                }

                send_invoice_to_email(
                    invoice,
                    email_subject,
                    email_body,
                    email_body_context
                )

            except Exception as email_error:
                # Log warning but don't fail the payment processing
                self.audit_context.log_step("order_payment_update", "email_failed", {
                    'email_error': str(email_error)
                })
                logger.error(f'Error while sending invoice email: {email_error}')

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("order_payment_update", "failed", {
                'order_id': order.get('id') if isinstance(order, dict) else None,
                'payment_id': payload.get('razorpay_payment_id') if isinstance(payload, dict) else None,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Order payment update failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def process_subscription_payment(self, rzp_sub: Dict[str, Any], invoice: Dict[str, Any], payment: Dict[str, Any]) -> None:
        """
        Process subscription payment with enhanced validation and error handling.

        Args:
            rzp_sub: Razorpay subscription data
            invoice: Invoice data dictionary
            payment: Payment data dictionary

        Raises:
            PaymentValidationError: If subscription payment data is invalid
            PaymentProcessingError: If subscription payment processing fails
        """
        try:
            # Log subscription payment processing start
            self.audit_context.log_step("subscription_payment_processing", "started")

            # Handle order processing
            order = {}
            try:
                order_id = invoice.get('order_id')
                if order_id:
                    order, fetched = self.razorpay.order.get_orders(order_id=order_id)

                    if not fetched:
                        error_info = order.get('error', {})
                        error_code = error_info.get('code', 'unknown')
                        error_desc = error_info.get('description', 'Unknown error')
                        logger.info(f'Error while fetching order details from Razorpay - {error_code}: {error_desc}')
                        order = {}
                    else:
                        # Process order
                        order['order_no'] = self.now
                        order['notes'] = rzp_sub.get('notes', {})
                        order['receipt'] = generate_sequence_no(SequenceType.RECEIPT_NUMBER)

                        existing_order = self.db.find(DBColls.RAZORPAY_ORDERS, {'id': order_id}, find_one=True)

                        if not existing_order:
                            self.db.insert(DBColls.RAZORPAY_ORDERS, [order])
                            order.pop('_id', None)
                        else:
                            finder = Finder()
                            finder.track_changes(order, existing_order)
                            update_query = copy.deepcopy(finder._get_update_query())

                            if update_query:
                                order = self.db.update(DBColls.RAZORPAY_ORDERS, {'id': order_id}, update_query, find_one_and_update=True)

            except Exception as order_error:
                self.audit_context.log_step("subscription_payment_processing", "order_processing_failed", {
                    'order_error': str(order_error)
                })
                logger.info(f'Error while processing order: {order_error}')
                order = {}

            # Get company and user data
            try:
                self._get_company()

                query = {
                    'user_role': UserRole.ADMIN_SEEKER.value,
                    'company_id': self.company_id,
                    'user_type': UserType.SEEKER,
                }
                user = self.db.find(DBColls.USERS, query, find_one=True)

            except Exception as user_error:
                raise PaymentProcessingError(
                    f"Failed to get company/user data: {str(user_error)}",
                    details={'user_error': str(user_error)}
                )

            # Calculate amounts and tax
            try:
                amount_paid = invoice.get('amount_paid')
                amount_in_rupees = amount_paid / 100
                update_query = back_compute_tax(amount_in_rupees)
                update_query = compute_gst_details(update_query, self.company.get('state_code'))
                amount_in_words = number_to_words(amount_in_rupees)

                update_query.update({
                    'customer_id': self.company.get('id'),
                    'amount_in_rupees': amount_in_rupees,
                    'amount_in_words': amount_in_words,
                    'notes': rzp_sub.get('notes', {}),
                    'order_no': order.get('order_no'),
                    'receipt': order.get('receipt')
                })

            except Exception as calculation_error:
                raise PaymentProcessingError(
                    f"Failed to calculate amounts and tax: {str(calculation_error)}",
                    details={'calculation_error': str(calculation_error)}
                )

            # Set customer details
            try:
                customer_details = {
                    'shipping_address': self.company.get('address'),
                    'billing_address': self.company.get('address'),
                    'customer_contact': self.company.get('phone'),
                    'customer_email': self.company.get('email'),
                    'customer_name': self.company.get('name'),
                    'contact': self.company.get('phone'),
                    'gstin': self.company.get('gstin'),
                    'name': user.get('user_name'),
                    'email': user.get('email')
                }

                if not invoice.get('customer_details', {}).get('id'):
                    customer_details['id'] = self.company.get('id')

                update_query['customer_details'] = customer_details

            except Exception as customer_error:
                raise PaymentProcessingError(
                    f"Failed to set customer details: {str(customer_error)}",
                    details={'customer_error': str(customer_error)}
                )

            # Update payment with invoice details
            try:
                pay_update_query = {}
                pay_query = {'id': payment.get('id')}

                if not payment.get('invoice_id'):
                    pay_update_query['invoice_id'] = invoice.get('id')

                if not payment.get('invoice_number'):
                    pay_update_query['invoice_number'] = invoice.get('invoice_number')

                if pay_update_query:
                    payment = self.db.update(DBColls.RAZORPAY_PAYMENTS, pay_query, pay_update_query, find_one_and_update=True)

            except Exception as payment_update_error:
                raise PaymentProcessingError(
                    f"Failed to update payment: {str(payment_update_error)}",
                    details={'payment_update_error': str(payment_update_error)}
                )

            # Process email and notifications
            try:
                notes = rzp_sub.get('notes', {})
                plan = self.db.find(DBColls.RAZORPAY_PLANS, {'id': rzp_sub.get('plan_id')}, find_one=True)

                plan_name = ''
                description = ''

                if plan and isinstance(plan, dict):
                    item_data = plan.get('item', {})
                    plan_name = item_data.get('name', '')
                    description = item_data.get('description', '')

                # Determine email type
                is_upgrade_plan = (notes.get('event') == SubscriptionEvent.UPGRADE)
                if is_upgrade_plan:
                    email_subject = EmailHeader.SUBSCRIPTION_UPGRADED
                    email_body = 'subscription_upgraded.html'
                    description = payment.get('description', description)
                else:
                    email_subject = EmailHeader.SUBSCRIPTION_ACTIVATED
                    email_body = 'subscription_activated.html'

                # Update invoice
                update_query['description'] = description
                invoice.update(update_query)

                if update_query:
                    invoice = self.db.update(DBColls.RAZORPAY_INVOICES, {'id': invoice.get('id')}, update_query, find_one_and_update=True)

                # Prepare email context
                amnt_in_rs = invoice.get('amount_in_rupees', 0)
                amnt_in_rs = int(amnt_in_rs) if (amnt_in_rs == int(amnt_in_rs)) else amnt_in_rs

                email_body_context = {
                    'order_no': invoice.get('order_no'),
                    'end_date': rzp_sub.get('current_end'),
                    'paid_at': payment.get('created_at'),
                    'description': description,
                    'sub_id': rzp_sub.get('id'),
                    'pay_id': payment.get('id'),
                    'plan_name': plan_name,
                    'price': amnt_in_rs
                }

                # Send notifications
                send_invoice_to_email(invoice, email_subject, email_body, email_body_context)
                send_payment_sms(invoice, self.company, user)

            except Exception as notification_error:
                # Log warning but don't fail processing
                self.audit_context.log_step("subscription_payment_processing", "notification_failed", {
                    'notification_error': str(notification_error)
                })
                logger.error(f'Error while sending invoice email & payment SMS: {notification_error}')

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("subscription_payment_processing", "failed", {
                'subscription_id': rzp_sub.get('id') if isinstance(rzp_sub, dict) else None,
                'invoice_id': invoice.get('id') if isinstance(invoice, dict) else None,
                'payment_id': payment.get('id') if isinstance(payment, dict) else None,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Subscription payment processing failed: {str(e)}",
                details={'original_error': str(e)}
            )

    @handle_payment_exception
    def compute_tax(self, taxable_amount: float = None, gst_details: bool = True) -> Dict[str, Any]:
        """
        Compute tax details with enhanced validation and error handling.

        Args:
            taxable_amount: Amount on which tax should be calculated
            gst_details: Whether to return detailed GST breakdown

        Returns:
            Dict[str, Any]: Tax calculation details or total amount

        Raises:
            PaymentValidationError: If tax calculation parameters are invalid
            PaymentProcessingError: If tax calculation fails
        """
        try:
            # Ensure company data is available
            if not hasattr(self, 'company') or not self.company:
                self._get_company()

            # Get tax percentage from settings
            try:
                tax_perc = settings.SERVICE_TAX_PERC
            except AttributeError:
                raise PaymentProcessingError(
                    "SERVICE_TAX_PERC not configured in settings",
                    details={'missing_setting': 'SERVICE_TAX_PERC'}
                )

            # Validate tax percentage
            if not isinstance(tax_perc, (int, float)) or tax_perc < 0 or tax_perc > 100:
                raise PaymentValidationError(
                    f"Invalid tax percentage: {tax_perc}. Must be between 0 and 100",
                    details={'tax_percentage': tax_perc}
                )

            # Determine taxable amount
            if taxable_amount is None:
                plan_amount = self.payload.get('plan_amount', 0) if self.payload else 0
                addon_amount = self.payload.get('addon_amount', 0) if self.payload else 0
                taxable_amount = plan_amount + addon_amount

            if taxable_amount <= 0:
                raise PaymentValidationError(
                    f"Taxable amount must be greater than zero: {taxable_amount}",
                    details={'taxable_amount': taxable_amount}
                )

            # Calculate tax amount
            tax_amount = taxable_amount * (tax_perc / 100)
            tax_amount = round(tax_amount, PRECISSION)

            # Calculate total amount
            total_amount = taxable_amount + tax_amount
            total_amount = round(total_amount, PRECISSION)

            # Return simple total if GST details not required
            if not gst_details:
                return total_amount

            # Initialize tax details structure
            tax_details = {
                'gst': 0,
                'gst_percent': tax_perc,
                'igst': 0,
                'igst_percent': 0,
                'cgst': 0,
                'cgst_percent': 0,
                'sgst': 0,
                'sgst_percent': 0
            }

            # Get state codes for tax calculation
            try:
                host_state_code = settings.HOST_STATE_CODE
            except AttributeError:
                raise PaymentProcessingError(
                    "HOST_STATE_CODE not configured in settings",
                    details={'missing_setting': 'HOST_STATE_CODE'}
                )

            customer_state_code = self.company.get('state_code')

            # Calculate state-specific taxes
            if (host_state_code != customer_state_code) or (not customer_state_code):
                # Inter-state transaction - IGST applicable
                tax_details.update({
                    'igst': tax_amount,
                    'igst_percent': tax_perc
                })
            else:
                # Intra-state transaction - CGST + SGST applicable
                split_tax_amount = float(tax_amount / 2.0)
                split_tax_amount = round(split_tax_amount, PRECISSION)
                split_tax_percent = float(tax_perc / 2.0)
                split_tax_percent = round(split_tax_percent, PRECISSION)

                tax_details.update({
                    'cgst': split_tax_amount,
                    'cgst_percent': split_tax_percent,
                    'sgst': split_tax_amount,
                    'sgst_percent': split_tax_percent
                })

            # Finalize tax details
            tax_details.update({
                'gst': tax_amount,
                'taxable_amount': taxable_amount,
                'total_amount': total_amount
            })

            return tax_details

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("tax_computation", "failed", {
                'taxable_amount': taxable_amount,
                'gst_details': gst_details,
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Tax computation failed: {str(e)}",
                details={'taxable_amount': taxable_amount, 'original_error': str(e)}
            )

    @handle_payment_exception
    def get_current_payable(self, tax_details: Dict[str, Any]) -> float:
        """
        Get current payable amount with enhanced validation and error handling.

        Args:
            tax_details: Tax calculation details dictionary

        Returns:
            float: Current payable amount

        Raises:
            PaymentValidationError: If tax details are invalid
            PaymentProcessingError: If payable calculation fails
        """
        try:
            # Validate total amount exists
            total_amount = tax_details.get('total_amount')
            if total_amount is None:
                raise PaymentValidationError(
                    "Tax details missing total_amount",
                    details={'tax_details_keys': list(tax_details.keys())}
                )

            # Check trial usage
            is_trial_used = False
            if hasattr(self, 'payload') and self.payload:
                is_trial_used = self.payload.get('is_trial_used', False)

                # Validate trial usage flag
                if not isinstance(is_trial_used, bool):
                    is_trial_used = False

            # Return full amount if trial is used
            if is_trial_used:
                return total_amount

            # Get trial amount from settings
            try:
                trial_amount = self.razorpay.saas_settings.get('trial_amount')

                # Use default if not configured
                if trial_amount is None:
                    trial_amount = TrialAmount.FIVE.value

                return trial_amount

            except Exception as trial_error:
                raise PaymentProcessingError(
                    f"Failed to get trial amount: {str(trial_error)}",
                    details={'trial_error': str(trial_error)}
                )

        except (PaymentValidationError, PaymentProcessingError):
            # Re-raise payment exceptions as-is
            raise
        except Exception as e:
            # Log unexpected error
            self.audit_context.log_step("payable_calculation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })

            raise PaymentProcessingError(
                f"Payable calculation failed: {str(e)}",
                details={'original_error': str(e)}
            )
