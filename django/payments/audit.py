import logging
import time
import uuid
from typing import Dict, Any, Optional
from enum import Enum
from django.conf import settings
from utils import get_uuid

logger = logging.getLogger('audit')


class AuditEventType(Enum):
    """Types of audit events for payment operations."""

    # Payment Events
    PAYMENT_INITIATED = "payment_initiated"
    PAYMENT_COMPLETED = "payment_completed"
    PAYMENT_FAILED = "payment_failed"
    PAYMENT_REFUNDED = "payment_refunded"

    # Subscription Events
    SUBSCRIPTION_CREATED = "subscription_created"
    SUBSCRIPTION_UPGRADED = "subscription_upgraded"
    SUBSCRIPTION_CANCELLED = "subscription_cancelled"
    SUBSCRIPTION_RENEWED = "subscription_renewed"

    # Security Events
    SIGNATURE_VERIFIED = "signature_verified"
    SIGNATURE_FAILED = "signature_failed"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    SECURITY_EVENT = "security_event"

    # System Events
    WEBHOOK_RECEIVED = "webhook_received"
    WEBHOOK_PROCESSED = "webhook_processed"
    API_REQUEST = "api_request"
    ERROR_OCCURRED = "error_occurred"



class PaymentAuditLogger:
    """Handles audit logging for payment operations."""

    def __init__(self):
        self.logger = logger
        self.enable_audit = getattr(settings, 'PAYMENT_AUDIT_ENABLED', True)
        self.sensitive_fields = {
            'card_number', 'cvv', 'password', 'secret', 'token', 'key'
        }

    def log_event(
        self,
        event_type: AuditEventType,
        user_id: Optional[str] = None,
        company_id: Optional[str] = None,
        payment_id: Optional[str] = None,
        amount: Optional[float] = None,
        currency: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ):
        """
        Log an audit event.

        Args:
            event_type: Type of event
            user_id: User identifier
            company_id: Company identifier
            payment_id: Payment identifier
            amount: Payment amount
            currency: Currency code
            details: Additional event details
            request_id: Request identifier for tracing
        """
        if not self.enable_audit:
            return

        audit_record = {
            'event_id': str(uuid.uuid4()),
            'event_type': event_type.value,
            'timestamp': int(time.time() * 1000),
            'user_id': user_id,
            'company_id': company_id,
            'payment_id': payment_id,
            'amount': amount,
            'currency': currency,
            'request_id': request_id or self._generate_request_id(),
            'details': self._sanitize_details(details or {}),
            'environment': getattr(settings, 'ENVIRONMENT_ENV', 'unknown')
        }

        self.logger.info(f"Payment Audit: {event_type.value}", extra={'audit_record': audit_record})

    def log_payment_flow(
        self,
        flow_type: str,
        step: str,
        status: str,
        user_id: Optional[str] = None,
        company_id: Optional[str] = None,
        payment_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Log payment flow steps for traceability.

        Args:
            flow_type: Type of payment flow (subscription, payg, etc.)
            step: Current step in the flow
            status: Status of the step (started, completed, failed)
            user_id: User identifier
            company_id: Company identifier
            payment_id: Payment identifier
            details: Additional step details
        """
        self.log_event(
            event_type=AuditEventType.API_REQUEST,
            user_id=user_id,
            company_id=company_id,
            payment_id=payment_id,
            details={
                'flow_type': flow_type,
                'step': step,
                'status': status,
                **self._sanitize_details(details or {})
            }
        )

    def log_security_event(
        self,
        event_type: AuditEventType,
        severity: str,
        description: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Log security-related events.

        Args:
            event_type: Type of security event
            severity: Severity level (low, medium, high, critical)
            description: Event description
            ip_address: Client IP address
            user_agent: Client user agent
            details: Additional security details
        """
        security_details = {
            'severity': severity,
            'description': description,
            'ip_address': ip_address,
            'user_agent': user_agent,
            **self._sanitize_details(details or {})
        }

        self.log_event(
            event_type=event_type,
            details=security_details
        )

    def log_webhook_event(
        self,
        webhook_id: str,
        event_type: str,
        status: str,
        processing_time: Optional[float] = None,
        error_message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Log webhook processing events.

        Args:
            webhook_id: Webhook identifier
            event_type: Type of webhook event
            status: Processing status
            processing_time: Time taken to process
            error_message: Error message if failed
            details: Additional webhook details
        """
        webhook_details = {
            'webhook_id': webhook_id,
            'webhook_event_type': event_type,
            'status': status,
            'processing_time_ms': processing_time,
            'error_message': error_message,
            **self._sanitize_details(details or {})
        }

        audit_event = AuditEventType.WEBHOOK_RECEIVED if status == 'received' else AuditEventType.WEBHOOK_PROCESSED

        self.log_event(
            event_type=audit_event,
            details=webhook_details
        )

    def log_error(
        self,
        error_type: str,
        error_message: str,
        user_id: Optional[str] = None,
        company_id: Optional[str] = None,
        payment_id: Optional[str] = None,
        stack_trace: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Log error events.

        Args:
            error_type: Type of error
            error_message: Error message
            user_id: User identifier
            company_id: Company identifier
            payment_id: Payment identifier
            stack_trace: Error stack trace
            details: Additional error details
        """
        error_details = {
            'error_type': error_type,
            'error_message': error_message,
            'stack_trace': stack_trace,
            **self._sanitize_details(details or {})
        }

        self.log_event(
            event_type=AuditEventType.ERROR_OCCURRED,
            user_id=user_id,
            company_id=company_id,
            payment_id=payment_id,
            details=error_details
        )

    def _sanitize_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize details to remove sensitive information.

        Args:
            details: Original details dictionary

        Returns:
            Dict: Sanitized details
        """
        sanitized = {}

        for key, value in details.items():
            if any(sensitive in key.lower() for sensitive in self.sensitive_fields):
                sanitized[key] = self._mask_sensitive_value(value)
            elif isinstance(value, dict):
                sanitized[key] = self._sanitize_details(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self._sanitize_details(item) if isinstance(item, dict) else item
                    for item in value
                ]
            else:
                sanitized[key] = value

        return sanitized

    def _mask_sensitive_value(self, value: Any) -> str:
        """Mask sensitive values for logging."""
        if isinstance(value, str):
            if len(value) <= 4:
                return "*" * len(value)
            return value[:2] + "*" * (len(value) - 4) + value[-2:]
        return "***MASKED***"

    def _generate_request_id(self) -> str:
        """Generate a unique request ID."""
        return get_uuid()

    def create_audit_context(
        self,
        user_id: Optional[str] = None,
        company_id: Optional[str] = None,
        request_id: Optional[str] = None
    ) -> 'AuditContext':
        """
        Create an audit context for tracking related events.

        Args:
            user_id: User identifier
            company_id: Company identifier
            request_id: Request identifier

        Returns:
            AuditContext: Context object for tracking
        """
        return AuditContext(
            audit_logger=self,
            user_id=user_id,
            company_id=company_id,
            request_id=request_id or self._generate_request_id()
        )


class AuditContext:
    """Context object for tracking related audit events."""

    def __init__(
        self,
        audit_logger: PaymentAuditLogger,
        user_id: Optional[str] = None,
        company_id: Optional[str] = None,
        request_id: Optional[str] = None
    ):
        self.audit_logger = audit_logger
        self.user_id = user_id
        self.company_id = company_id
        self.request_id = request_id
        self.start_time = time.time()

    def log_event(
        self,
        event_type: AuditEventType,
        payment_id: Optional[str] = None,
        amount: Optional[float] = None,
        currency: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """Log an event with context information."""
        self.audit_logger.log_event(
            event_type=event_type,
            user_id=self.user_id,
            company_id=self.company_id,
            payment_id=payment_id,
            amount=amount,
            currency=currency,
            details=details,
            request_id=self.request_id
        )

    def log_step(self, step: str, status: str, details: Optional[Dict[str, Any]] = None):
        """Log a step in the payment flow."""
        self.audit_logger.log_payment_flow(
            flow_type="payment_processing",
            step=step,
            status=status,
            user_id=self.user_id,
            company_id=self.company_id,
            details=details
        )

    def get_elapsed_time(self) -> float:
        """Get elapsed time since context creation."""
        return (time.time() - self.start_time) * 1000  # in milliseconds
