import logging
from utils.mongo import MongoUtility
from utils.constants import DBColls, RazorpayPaymentStatus

logger = logging.getLogger('application')


class RequestFilters(object):

    def __init__(self, request=None):
        self.db = MongoUtility()
        self.request = request

    def _get_data_filter(self, coll):
        coll_filter_map = {
            DBColls.RAZORPAY_PAYMENTS: {
                "id": 1,
                "order_id": 1,
                "invoice_id": 1,
                "description": 1,
                "created_at": 1,
                "method": 1,
                "status": 1,
                "currency": 1,
                "amount": 1,
                "upi": 1,
                "subscription_id": 1,
                "card.type": 1,
                "card.number": 1
            }
        }

        data_filter = {'_id': 0}
        try:
            data_filter.update(coll_filter_map[coll])
        except KeyError:
            pass
        return data_filter

    def _filter_payments_by_time_range(self, query, start_date, end_date):
        if start_date and end_date:
            if start_date > end_date:
                raise ValueError('Invalid dates. start_date cannot be greater than end_date.')
            query['created_at'] = {'$gte': start_date, '$lte': end_date}
        return query

    def _filter_payments_by_search_term(self, query, search_field, search_term):
        if search_term:
            query_value = search_term
            if ',' in search_term:
                search_terms = [x.strip() for x in search_term.split(',') if x.strip()]
                if not search_terms:
                    return query
                query_value = {'$in': search_terms}

            query[search_field] = query_value
        return query

    def _get_payment_ids(self):
        query = {
            'notes.user_id': self.request.user_id,
            'notes.company_id': self.request.company_id
        }
        order_ids = self.db.distinct(DBColls.RAZORPAY_ORDERS, 'id', query)
        query = {
            'order_id': {
                '$in': order_ids
            },
            'status': {
                '$in': [
                    RazorpayPaymentStatus.CAPTURED,
                    RazorpayPaymentStatus.FAILED
                ]
            }
        }
        payment_ids = self.db.distinct(DBColls.RAZORPAY_PAYMENTS, 'id', query)
        return payment_ids

    def _get_processed_payments_data(self, payments):
        payments_list = []
        for payment in payments:
            payment['amount'] = float(payment['amount'] / 100)
            payment['created_at'] = payment['created_at'] * 1000
            payments_list.append(payment)
        return payments_list

    def get_payments(self, query, limit, offset, sort_by, sort_value):
        data_filter = self._get_data_filter(coll=DBColls.RAZORPAY_PAYMENTS)
        payments = self.db.find(DBColls.RAZORPAY_PAYMENTS, query, data_filter, sort=[(sort_by, sort_value)]).limit(limit).skip(offset)
        count = payments.count()
        payments_list = self._get_processed_payments_data(payments)
        return payments_list, count

    def get_total_counts(self, query):
        query['status'] = {
            '$in': [
                RazorpayPaymentStatus.CAPTURED,
                RazorpayPaymentStatus.FAILED
            ]
        }
        group_query = {"_id": "$status", "count": {"$sum": 1}}
        project_query = {"_id": 0, "status": "$_id", "count": 1}
        aggregate_result = list(self.db.aggregate(DBColls.RAZORPAY_PAYMENTS, query, group_query, project_query))
        return aggregate_result

    def run(self, request, get_filter_query=False):
        params = request.GET

        payment_status = str(params.get('payment_status') or '')

        limit = int(params.get('limit', 10))
        offset = int(params.get('offset', 0))

        start_date = int(params.get('start_date', 0))
        end_date = int(params.get('end_date', 0))

        search_by = params.get('search_by') or 'id'
        search_term = params.get('search_term', '').strip()

        sort_by = params.get('sort_by') or 'created_at'
        sort_value = int(params.get('sort_value') or -1)

        query = {
            'id': {
                '$in': self._get_payment_ids()
            }
        }

        if payment_status:
            if payment_status not in [RazorpayPaymentStatus.CAPTURED, RazorpayPaymentStatus.FAILED]:
                raise ValueError('Invalid payment status value received.')

            query['status'] = payment_status

        query = self._filter_payments_by_time_range(query, start_date, end_date)
        query = self._filter_payments_by_search_term(query, search_by, search_term)

        if get_filter_query:
            return query

        return self.get_payments(query, limit, offset, sort_by, sort_value)