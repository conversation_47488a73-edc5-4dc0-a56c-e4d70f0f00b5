import os
import logging
from django.conf import settings
from django.shortcuts import render
from rest_framework import status
from rest_framework.views import APIView
from pydantic import ValidationError
from django.http import HttpResponse
from utils.mongo import MongoUtility
from utils.constants import (
    RazorpayInvoiceStatus,
    RazorpayPaymentStatus,
    SubscriptionStatus,
    SubscriptionEvent,
    SuccessMessages,
    ErrorMessages,
    AppDomainID,
    OrderStatus,
    AppPathID,
    DBColls
)
from utils import (
    convert_html_template_to_pdf,
    format_error_response,
    format_response,
    Memoization,
    ConfigError,
    RedisUtils
)
from authn import AuthenticateSeekerAdmin
from .request_validators import (
    PaymentCompletePaygPayloadValidator,
    PaymentCompletePayloadValidator,
    PaymentLinkPayloadValidator,
    ComputeTaxPayloadValidator,
    PaygLinkPayloadValidator
)
from .request_filters import RequestFilters
from .request_utils import RequestUtils
from .razorpay import Ra<PERSON>pay

logger = logging.getLogger('application')


def test_invoice(request):
    return render(request, 'invoice.html', {})


class PaymentsListingApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        try:
            inst = RequestFilters(request)
            try:
                payments_list, count = inst.run(request)
            except ValueError as e:
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    str(e)
                )

            data = {
                'payments': payments_list,
                'count': count
            }
            return format_response(status.HTTP_200_OK, data, 'Payments retreived successfully')
        except Exception as e:
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                str(e)
            )


class PaygListingApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        try:
            params = request.GET
            db = MongoUtility()
            limit = int(params.get('limit', 10))
            offset = int(params.get('offset', 0))

            query = {'status': 'created'}
            orders = db.find(DBColls.RAZORPAY_ORDERS, query).limit(limit).skip(offset)
            count = orders.count()
            orders_list = list(orders)

            data = {
                'orders': orders_list,
                'count': count
            }
            return format_response(status.HTTP_200_OK, data, 'Pending orders retreived successfully')
        except Exception as e:
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                str(e)
            )


class PaymentLinkApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        response = {}
        redis_inst = RedisUtils()
        inst = RequestUtils(request)

        try:
            inst.validate_payload(PaymentLinkPayloadValidator)
        except ValueError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                str(e)
            )

        lock_key = inst._generate_lock_key()
        if not redis_inst.redis_conn.set(lock_key, 'locked'):
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Processing payments.'
            )

        try:
            payments_data = inst.process_subscription_payments()
            response.update(payments_data)
        except (ValueError, KeyError, ConfigError) as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                str(e)
            )
        except Exception as e:
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                str(e)
            )
        finally:
            redis_inst.redis_conn.delete(lock_key)

        message, redirect_to = inst.process_response_message()
        return format_response(
            status.HTTP_200_OK,
            response,
            message,
            redirect_to
        )


class PaymentLinkPaygApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        db = MongoUtility()
        inst = RequestUtils(request)
        inst.sclen._get_company()
        order_id = kwargs['order_id']
        inst.sclen._event = SubscriptionEvent.PAYG
        query = {'id': order_id, 'status': OrderStatus.CREATED}
        order_obj = db.find(DBColls.RAZORPAY_ORDERS, query, find_one=True)
        if not order_obj:
            return format_error_response(
                status.HTTP_404_NOT_FOUND,
                ErrorMessages.ORDER_DOES_NOT_EXIST
            )

        payload = order_obj
        inst.payments_data = order_obj
        payload['event'] = SubscriptionEvent.PAYG
        setattr(inst, 'payload', payload)
        checkout_options = inst._get_checkout_options(checkout_id=order_obj['id'])
        data = {'checkout_options': checkout_options}
        return format_response(
            status.HTTP_200_OK,
            data,
            SuccessMessages.PROCEED_TO_PAY
        )

    def post(self, request, *args, **kwargs):
        response = {}
        redis_inst = RedisUtils()
        inst = RequestUtils(request)

        try:
            inst.validate_payload(PaygLinkPayloadValidator)
        except ValueError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                str(e)
            )

        lock_key = inst._generate_lock_key()
        if not redis_inst.redis_conn.set(lock_key, 'locked'):
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                'Processing payments.'
            )

        try:
            payments_data = inst.process_payg_payments()
            response.update(payments_data)
        except (ValueError, KeyError, ConfigError) as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                str(e)
            )
        except Exception as e:
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                str(e)
            )
        finally:
            redis_inst.redis_conn.delete(lock_key)

        message, redirect_to = inst.process_response_message()
        return format_response(
            status.HTTP_200_OK,
            response,
            message,
            redirect_to
        )


class PaymentCompleteApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        # Not used (alternate method of veerifying payment & completing payment)
        db = MongoUtility()
        query = {'id': kwargs['subscription_id']}
        subscription = db.find(DBColls.SUBSCRIPTIONS, query, find_one=True)
        if not bool(subscription):
            return format_error_response(
                status.HTTP_404_NOT_FOUND,
                'Invalid payment request. Subscription does not exist.'
            )

        redirect_to = self.get_redirect_url()
        response = {'success': False}
        try:
            sort = [('_id', -1)]
            query = {'subscription_id': subscription['razorpay_sub_id']}
            invoice = db.find(DBColls.RAZORPAY_INVOICES, query, sort=sort, find_one=True)
            if not invoice:
                return format_response(
                    status.HTTP_200_OK,
                    response,
                    SuccessMessages.GENERATING_INVOICE
                )

            payment = db.find(DBColls.RAZORPAY_PAYMENTS, query, sort=sort, find_one=True)
            if not payment:
                return format_response(
                    status.HTTP_200_OK,
                    response,
                    SuccessMessages.CAPTURING_PAYMENTS
                )

            invoice_paid = (invoice['status'] == RazorpayInvoiceStatus.PAID)
            payment_captured = (payment['status'] == RazorpayPaymentStatus.CAPTURED)
            subscription_active = (subscription['status_id'] == SubscriptionStatus.ACTIVE.value)
            response['success'] = (invoice_paid and payment_captured and subscription_active)
            return format_response(
                status.HTTP_200_OK,
                response,
                SuccessMessages.SUBSCRIPTION_ACTIVATED,
                redirect_to
            )
        except Exception as e:
            logger.error(f'[PAYMENT_COMPLETE] Unhandled exception: {e}')
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                'An unknown error occured. We are fixing, please try again later.'
            )

    def post(self, request, *args, **kwargs):
        db = MongoUtility()
        subscription_id = kwargs['subscription_id']
        subscription = db.find(DBColls.SUBSCRIPTIONS, {'id': subscription_id}, find_one=True)
        if not subscription:
            return format_error_response(
                status.HTTP_404_NOT_FOUND,
                ErrorMessages.SUBSCRIPTION_NOT_FOUND
            )

        raw_payload = request.data
        try:
            payload = PaymentCompletePayloadValidator(**raw_payload).model_dump()
        except ValidationError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                pydantic_error=e
            )

        redirect_to = self.get_redirect_url()
        try:
            razorpay = Razorpay()
            inst = RequestUtils(request)
            razorpay.client.utility.verify_subscription_payment_signature(payload)
            inst.sclen.update_payments(payload)
            return format_response(
                status.HTTP_200_OK,
                {'success': True},
                SuccessMessages.SUBSCRIPTION_ACTIVATED,
                redirect_to
            )
        except (ValueError, ConfigError) as e:
            error_msg = 'Error while verifying subscription payment'
            logger.error(f'{error_msg}. Reason: {e}')
            return format_response(
                status.HTTP_200_OK,
                {'success': False},
                ErrorMessages.VERIFYING_PAYMENT,
                redirect_to
            )

    def get_redirect_url(self):
        domain_id, path_id = AppDomainID.ADMIN, AppPathID.ADMIN_PROFILE
        redirect_to = Memoization.get_app_url_data(domain_id, path_id)
        return redirect_to


class PaymentCompletePaygApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        db = MongoUtility()

        order_id = kwargs['order_id']
        order_obj = db.find(DBColls.RAZORPAY_ORDERS, {'id': order_id}, find_one=True)
        if not order_obj:
            return format_error_response(
                status.HTTP_404_NOT_FOUND,
                ErrorMessages.ORDER_DOES_NOT_EXIST
            )

        raw_payload = request.data
        try:
            payload = PaymentCompletePaygPayloadValidator(**raw_payload).model_dump()
        except ValidationError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                pydantic_error=e
            )

        redirect_to = self.get_redirect_url()
        try:
            razorpay = Razorpay()
            inst = RequestUtils(request)
            razorpay.client.utility.verify_payment_signature(payload)
            inst.sclen.update_order_payments(order_obj, payload)
            return format_response(
                status.HTTP_200_OK,
                {'success': True},
                SuccessMessages.PAYMENT_SUCCESS,
                redirect_to
            )
        except (ValueError, ConfigError) as e:
            error_msg = 'Error while verifying PAYG payment.'
            logger.error(f'{error_msg}. Reason: {e}')
            return format_response(
                status.HTTP_200_OK,
                {'success': False},
                ErrorMessages.VERIFYING_PAYMENT,
                redirect_to
            )

    def get_redirect_url(self):
        domain_id, path_id = AppDomainID.ADMIN, AppPathID.ADMIN_PROFILE
        redirect_to = Memoization.get_app_url_data(domain_id, path_id)
        return redirect_to


class SubscriptionPauseApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        try:
            subscription_id = kwargs['subscription_id']
            db = MongoUtility()
            query = {'id': subscription_id}

            if subscription_id.startswith('sub_'):
                sub_id_key = 'id'
                collection = DBColls.RAZORPAY_SUBSCRIPTIONS
            else:
                collection = DBColls.SUBSCRIPTIONS
                sub_id_key = 'razorpay_sub_id'

            subscription = db.find(collection, query, find_one=True)
            if not subscription:
                return format_error_response(
                    status.HTTP_404_NOT_FOUND,
                    ErrorMessages.SUBSCRIPTION_NOT_FOUND
                )

            razorpay = Razorpay()
            razorpay_sub_id = subscription[sub_id_key]
            rzp_subs, paused = razorpay.subscription.pause_subscription(razorpay_sub_id)

            if not paused:
                error_code = rzp_subs['error']['code']
                error_desc = rzp_subs['error']['description']
                error_msg = f'{error_code}: {error_desc}'
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    error_msg
                )

            return format_response(
                status.HTTP_200_OK,
                {'paused': paused},
                'Subscription paused sucessfully.'
            )
        except Exception as e:
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                str(e)
            )


class SubscriptionResumeApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        try:
            subscription_id = kwargs['subscription_id']
            db = MongoUtility()
            query = {'id': subscription_id}

            if subscription_id.startswith('sub_'):
                sub_id_key = 'id'
                collection = DBColls.RAZORPAY_SUBSCRIPTIONS
            else:
                collection = DBColls.SUBSCRIPTIONS
                sub_id_key = 'razorpay_sub_id'

            subscription = db.find(collection, query, find_one=True)
            if not subscription:
                return format_error_response(
                    status.HTTP_404_NOT_FOUND,
                    ErrorMessages.SUBSCRIPTION_NOT_FOUND
                )

            razorpay = Razorpay()
            razorpay_sub_id = subscription[sub_id_key]
            rzp_subs, resumed = razorpay.subscription.resume_subscription(razorpay_sub_id)

            if not resumed:
                error_code = rzp_subs['error']['code']
                error_desc = rzp_subs['error']['description']
                error_msg = f'{error_code}: {error_desc}'
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    error_msg
                )

            return format_response(
                status.HTTP_200_OK,
                {'resumed': resumed},
                'Subscription resumed sucessfully.'
            )
        except Exception as e:
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                str(e)
            )


class SubscriptionCancelApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        try:
            subscription_id = kwargs['subscription_id']
            db = MongoUtility()
            query = {'id': subscription_id}

            if subscription_id.startswith('sub_'):
                sub_id_key = 'id'
                collection = DBColls.RAZORPAY_SUBSCRIPTIONS
            else:
                collection = DBColls.SUBSCRIPTIONS
                sub_id_key = 'razorpay_sub_id'

            subscription = db.find(collection, query, find_one=True)
            if not subscription:
                return format_error_response(
                    status.HTTP_404_NOT_FOUND,
                    ErrorMessages.SUBSCRIPTION_NOT_FOUND
                )

            razorpay = Razorpay()
            razorpay_sub_id = subscription[sub_id_key]
            rzp_subs, cancelled = razorpay.subscription.cancel_subscription(razorpay_sub_id)

            if not cancelled:
                error_code = rzp_subs['error']['code']
                error_desc = rzp_subs['error']['description']
                error_msg = f'{error_code}: {error_desc}'
                return format_error_response(
                    status.HTTP_400_BAD_REQUEST,
                    error_msg
                )

            return format_response(
                status.HTTP_200_OK,
                {'cancelled': cancelled},
                'Subscription cancelled sucessfully.'
            )
        except Exception as e:
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                str(e)
            )


class PaymentInvoiceDownloadApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def get(self, request, *args, **kwargs):
        invoice_id = kwargs['invoice_id']

        db = MongoUtility()
        query = {'id': invoice_id}

        invoice = db.find(DBColls.RAZORPAY_INVOICES, query, find_one=True)
        if not invoice:
            return HttpResponse(status=404, content=ErrorMessages.INVOICE_NOT_FOUND)

        invoice_css_url = os.path.join(settings.STATIC_ROOT, 'css/invoice.css')
        pdf_data = convert_html_template_to_pdf(invoice, 'invoice.html', [invoice_css_url])

        response = HttpResponse(pdf_data, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="{}.pdf"'.format(invoice['invoice_number'])
        return response


class ComputeTaxApiView(APIView):
    authentication_classes = (AuthenticateSeekerAdmin, )

    def post(self, request, *args, **kwargs):
        inst = RequestUtils(request)
        try:
            inst.validate_payload(ComputeTaxPayloadValidator)
        except ValueError as e:
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                str(e)
            )

        try:
            tax_details = inst.sclen.compute_tax()
            current_payable = inst.sclen.get_current_payable(tax_details)
            tax_details['current_payable'] = current_payable
            data = {'tax_details': tax_details}
            return format_response(
                status.HTTP_200_OK,
                data,
                'Tax details retreived successfully.'
            )
        except (ValueError, ConfigError) as e:
            error_msg = 'Error while computing tax.'
            logger.error(f'{error_msg}. Reason: {e}')
            return format_error_response(
                status.HTTP_400_BAD_REQUEST,
                f'Error while computing tax: {e}'
            )
        except Exception as e:
            error_msg = f'Unhandled exception: {e}'
            logger.error(error_msg)
            return format_error_response(
                status.HTTP_500_INTERNAL_SERVER_ERROR,
                'Something went wrong, please try again later'
            )