import hmac
import time
import hashlib
import logging
from typing import Dict, Any
from django.conf import settings
from .exceptions import (
    PaymentSecurityError,
    PaymentRateLimitError,
    PaymentErrorCode
)
from utils import RedisUtils

logger = logging.getLogger('application')

class PaymentSecurityManager:
    """Manages security aspects of payment processing."""

    def __init__(self):
        self.redis_inst = RedisUtils()
        self.rate_limit_cache_prefix = "payment_rate_limit"
        self.security_cache_prefix = "payment_security"

    def validate_request_signature(self, payload: str, signature: str, secret: str) -> bool:
        """
        Validate webhook/payment signature for authenticity.

        Args:
            payload: Raw request payload
            signature: Provided signature
            secret: Secret key for validation

        Returns:
            bool: True if signature is valid

        Raises:
            PaymentSecurityError: If signature validation fails
        """
        try:
            expected_signature = hmac.new(
                secret.encode('utf-8'),
                payload.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # Use constant-time comparison to prevent timing attacks
            if not hmac.compare_digest(signature, expected_signature):
                raise PaymentSecurityError(
                    "Signature verification failed",
                    error_code=PaymentErrorCode.SIGNATURE_VERIFICATION_FAILED,
                    details={
                        'provided_signature': signature[:10] + "...",  # Log only first 10 chars
                        'payload_length': len(payload)
                    }
                )

            return True

        except Exception as e:
            logger.error(f"Signature validation error: {str(e)}")
            raise PaymentSecurityError(
                "Failed to validate request signature",
                error_code=PaymentErrorCode.SIGNATURE_VERIFICATION_FAILED
            )

    def check_rate_limit(self, identifier: str, limit: int = 100, window: int = 3600) -> bool:
        """
        Check if request is within rate limits.

        Args:
            identifier: Unique identifier (IP, user_id, etc.)
            limit: Maximum requests allowed
            window: Time window in seconds

        Returns:
            bool: True if within limits

        Raises:
            PaymentRateLimitError: If rate limit exceeded
        """
        cache_key = f"{self.rate_limit_cache_prefix}:{identifier}"
        current_time = int(time.time())

        # Get current request count
        request_data = self.redis_inst.get_data(cache_key) or {'count': 0, 'window_start': current_time}

        # Reset window if expired
        if current_time - request_data['window_start'] >= window:
            request_data = {'count': 1, 'window_start': current_time}
        else:
            request_data['count'] += 1

        # Check if limit exceeded
        if request_data['count'] > limit:
            retry_after = window - (current_time - request_data['window_start'])
            raise PaymentRateLimitError(
                f"Rate limit exceeded for {identifier}",
                retry_after=retry_after,
                details={
                    'current_count': request_data['count'],
                    'limit': limit,
                    'window': window
                }
            )

        # Update cache
        self.redis_inst.set_data(cache_key, request_data)
        return True

    def sanitize_input(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize input data to prevent injection attacks.

        Args:
            data: Input data dictionary

        Returns:
            Dict: Sanitized data
        """
        sanitized = {}

        for key, value in data.items():
            if isinstance(value, str):
                # Remove potentially dangerous characters
                sanitized_value = self._sanitize_string(value)
                sanitized[key] = sanitized_value
            elif isinstance(value, dict):
                sanitized[key] = self.sanitize_input(value)
            elif isinstance(value, list):
                sanitized[key] = [
                    self._sanitize_string(item) if isinstance(item, str) else item
                    for item in value
                ]
            else:
                sanitized[key] = value

        return sanitized

    def _sanitize_string(self, value: str) -> str:
        """Sanitize string value."""
        if not isinstance(value, str):
            return value

        # Remove null bytes and control characters
        sanitized = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')

        # Limit length to prevent DoS
        max_length = getattr(settings, 'PAYMENT_MAX_STRING_LENGTH', 1000)
        if len(sanitized) > max_length:
            sanitized = sanitized[:max_length]
            logger.warning(f"String truncated to {max_length} characters")

        return sanitized

    def validate_amount(self, amount: float, currency: str = 'INR') -> bool:
        """
        Validate payment amount for security.

        Args:
            amount: Payment amount
            currency: Currency code

        Returns:
            bool: True if amount is valid

        Raises:
            PaymentSecurityError: If amount is invalid
        """
        # Check for reasonable amount limits
        min_amount = getattr(settings, 'PAYMENT_MIN_AMOUNT', 1.0)
        max_amount = getattr(settings, 'PAYMENT_MAX_AMOUNT', 1000000.0)

        if amount < min_amount:
            raise PaymentSecurityError(
                f"Amount {amount} is below minimum allowed ({min_amount})",
                error_code=PaymentErrorCode.INVALID_AMOUNT,
                details={'amount': amount, 'min_amount': min_amount}
            )

        if amount > max_amount:
            raise PaymentSecurityError(
                f"Amount {amount} exceeds maximum allowed ({max_amount})",
                error_code=PaymentErrorCode.INVALID_AMOUNT,
                details={'amount': amount, 'max_amount': max_amount}
            )

        # # Check for suspicious patterns (e.g., very precise amounts that might indicate testing)
        # if self._is_suspicious_amount(amount):
        #     logger.warning(f"Suspicious amount detected: {amount}")
        #     self._log_security_event("suspicious_amount", {'amount': amount})

        return True

    def _is_suspicious_amount(self, amount: float) -> bool:
        """Check if amount follows suspicious patterns."""
        # Check for test amounts (like 1.00, 0.01, etc.)
        test_amounts = [0.01, 1.00, 10.00, 100.00]
        return amount in test_amounts

    def validate_customer_data(self, customer_data: Dict[str, Any]) -> bool:
        """
        Validate customer data for security issues.

        Args:
            customer_data: Customer information

        Returns:
            bool: True if data is valid

        Raises:
            PaymentSecurityError: If data is invalid
        """
        required_fields = ['name', 'email']

        for field in required_fields:
            if field not in customer_data or not customer_data[field]:
                raise PaymentSecurityError(
                    f"Required field '{field}' is missing or empty",
                    error_code=PaymentErrorCode.INVALID_CUSTOMER_DATA,
                    details={'missing_field': field}
                )

        # Validate email format
        email = customer_data.get('email', '')
        if not self._is_valid_email(email):
            raise PaymentSecurityError(
                "Invalid email format",
                error_code=PaymentErrorCode.INVALID_CUSTOMER_DATA,
                details={'email': email}
            )

        return True

    def _is_valid_email(self, email: str) -> bool:
        """Basic email validation."""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))

    def _log_security_event(self, event_type: str, details: Dict[str, Any]):
        """Log security events for monitoring."""
        logger.warning(
            f"Security event: {event_type}",
            extra={
                'event_type': event_type,
                'details': details,
                'timestamp': int(time.time() * 1000)
            }
        )

    def check_ip_whitelist(self, ip_address: str) -> bool:
        """
        Check if IP address is in whitelist (for webhook endpoints).

        Args:
            ip_address: Client IP address

        Returns:
            bool: True if IP is allowed
        """
        whitelist = getattr(settings, 'PAYMENT_IP_WHITELIST', [])

        if not whitelist:
            return True  # No whitelist configured

        if ip_address not in whitelist:
            raise PaymentSecurityError(
                f"IP address {ip_address} not in whitelist",
                error_code=PaymentErrorCode.UNAUTHORIZED_ACCESS,
                details={'ip_address': ip_address}
            )

        return True
