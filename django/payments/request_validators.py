import logging
from pydantic import (
    model_validator,
    PositiveFloat,
    PositiveInt,
    BaseModel,
    confloat,
    EmailStr,
    Field
)
from typing import Optional, List
from typing_extensions import Self
from utils.constants import (
    SubscriptionEvent,
    BillingCycle,
    Currency
)
from .payment_constants import (
    NOTIFY_PLINK_VIA_WHATSAPP,
    NOTIFY_PLINK_VIA_EMAIL,
    NOTIFY_PLINK_VIA_SMS,
    REMINDER_ENABLE
)

logger = logging.getLogger('application')


class CreateRazorpayPlanNoteSchema(BaseModel):
    plan_id: str

    class Config:
        extra = 'allow'


class CreateRazorpayPlanItem(BaseModel):
    name: str
    amount: PositiveInt
    currency: str
    description: str


class CreateRazorpayPlanValidator(BaseModel):
    period: str
    interval: int
    item: CreateRazorpayPlanItem
    notes: CreateRazorpayPlanNoteSchema


class UpgradeSubscriptionAddons(BaseModel):
    id: str
    qty: int


class PaymentLinkPayloadValidator(BaseModel):
    current_subscription_id: str
    event: str = SubscriptionEvent.SUBSCRIBE
    new_plan_id: str = ''
    addons: list[UpgradeSubscriptionAddons] = Field(default_factory=list)
    get_payments_data: bool = False

    @model_validator(mode='after')
    def validate_upgrade_plan(self) -> Self:
        if (self.event == SubscriptionEvent.UPGRADE):
            if not self.new_plan_id:
                raise ValueError('Please select a plan to upgrade.')

        return self


class PaygItemsValidator(BaseModel):
    name: str
    description: str
    quantity: PositiveInt
    unit_price: PositiveFloat
    amount: PositiveInt


class PaygLinkPayloadValidator(BaseModel):
    current_subscription_id: str
    event: str = SubscriptionEvent.PAYG
    items: List[PaygItemsValidator]
    get_payments_data: bool = True

    @model_validator(mode='after')
    def validate_items(self) -> Self:
        if len(self.items) <= 0:
            raise ValueError('Please select/add an item')
        return self


class PaymentCompletePayloadValidator(BaseModel):
    razorpay_subscription_id: str
    razorpay_payment_id: str
    razorpay_signature: str


class PaymentCompletePaygPayloadValidator(BaseModel):
    razorpay_order_id: str
    razorpay_payment_id: str
    razorpay_signature: str


class CreateRazorpayCustomerCustomerNotesValidator(BaseModel):
    address: str
    state: str
    city: str
    pincode: str
    pan: str
    company_type: PositiveInt
    company_id: str


class CreateRazorpayCustomerPayloadValidator(BaseModel):
    name: str
    contact: str
    email: EmailStr
    gstin: str
    fail_existing: int = 1
    notes: CreateRazorpayCustomerCustomerNotesValidator


class CreateSubscriptionAddonItemValidator(BaseModel):
    name: str
    amount: PositiveInt
    currency: str


class CreateSubscriptionAddonValidator(BaseModel):
    item: CreateSubscriptionAddonItemValidator
    quantity: int = 1


class CreateSubscriptionNoteValidator(BaseModel):
    user_id: str
    company_id: str
    subscription_id: str

    class Config:
        extra = "allow"


class CreateSubscriptionNotifyValidator(BaseModel):
    notify_phone: str = ''
    notify_email: EmailStr


class CreateSubscriptionPayloadValidator(BaseModel):
    plan_id: str
    customer_notify: int
    quantity: int = 1
    total_count: int
    expire_by: int
    addons: list[CreateSubscriptionAddonValidator] = Field(default_factory=list)
    notes: CreateSubscriptionNoteValidator
    # notify_info: CreateSubscriptionNotifyValidator

    class Config:
        extra = "allow"


class OrderNotesValidator(BaseModel):
    user_id: str
    company_id: str
    subscription_id: str

    class Config:
        extra = "allow"


class CreateOrderPayloadValidator(BaseModel):
    amount: PositiveInt
    currency: str = Currency.INR
    receipt: str
    notes: OrderNotesValidator


class PaymentLinkNotifyValidator(BaseModel):
    sms: bool = NOTIFY_PLINK_VIA_SMS
    email: bool = NOTIFY_PLINK_VIA_EMAIL
    whatsapp: bool = NOTIFY_PLINK_VIA_WHATSAPP


class PaymentLinkNotesValidator(BaseModel):
    current_plan: str = ''
    current_plan_id: str = ''
    current_subscription_id: str = ''
    billing_cycle: str = BillingCycle.ONE_TIME
    for_entity: str = Field(default='order')
    validity: str = 'lifetime'
    prorated: bool = False


class CreatePaymentLinkPayloadValidator(BaseModel):
    amount: PositiveInt
    currency: str = Currency.INR
    customer_id: str
    description: str
    reference_id: str = ""
    expire_by: int = 0
    notify: PaymentLinkNotifyValidator = Field(default_factory=PaymentLinkNotifyValidator)
    notes: PaymentLinkNotesValidator = Field(default_factory=dict)
    reminder_enable: bool = REMINDER_ENABLE
    callback_url: Optional[str] = ""
    callback_method: Optional[str] = ""

    @model_validator(mode='after')
    def add_callback_method(self) -> Self:
        if self.callback_url:
            self.callback_method = 'get'
        return self


class ComputeTaxPayloadValidator(BaseModel):
    plan_amount: PositiveFloat
    addon_amount: Optional[confloat(ge=0)] = 0.0
    is_trial_used: bool = False