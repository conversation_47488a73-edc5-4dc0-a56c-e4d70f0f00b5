import logging
from typing import Dict, Any
from utils.mongo import MongoUtility
from utils.constants import (
    RazorpayPaymentStatus,
    SubscriptionStatus,
    SubscriptionEvent,
    SequenceType,
    BillingCycle,
    Currency,
    DBColls
)
from utils import (
    generate_sequence_no,
    DateUtil
)
from .payment_utils import (
    convert_to_unix_secs,
    add_days
)
from schema import (
    RazorpaySubscriptionSchema
)
from .payment_constants import SUBSCRIPTION_LINK_EXPIRY
from .request_validators import (
    CreateSubscriptionPayloadValidator
)
from .razorpay import Razorpay
from .exceptions import (
    PaymentBusinessLogicError,
    handle_payment_exception,
    PaymentProcessingError,
    PaymentValidationError
)
from .resilience import with_resilience
from .audit import PaymentAuditLogger

logger = logging.getLogger('application')
audit_logger = PaymentAuditLogger()


class SubscriptionUtils(object):
    """
    Enhanced subscription management with modern industry standards.

    Subscribe Flow:
        1. Create new subscription in Razorpay with current plan's addons (if enabled)
        2. Apply discount (if applicable)
        3. Charge subscription amount via:
            a. Razorpay's hosted subscription payments & authorization page
            b. Razorpay's checkout plugin in FE
        4. Verify payments via Razorpay Subscription webhooks
        5. Enhanced audit logging and error handling

    Upgrade Flow:
        1. Cancel current subscription & create new subscription in Razorpay
           with new plan's addons (if enabled)
        2. Apply prorated amount & discount (if applicable)
        3. Update new razorpay subscription details in the current subscription
        4. Update new plan's details into the current subscription
        5. Charge subscription amount via:
            a. Razorpay's hosted subscription payments & authorization page
            b. Razorpay's checkout plugin in FE
        6. Verify payments via Razorpay Subscription webhooks
        7. Enhanced monitoring and resilience patterns
    """

    def __init__(self, sclen):
        self.sclen = sclen
        self.db = MongoUtility()
        self.razorpay = Razorpay()
        self.now = DateUtil.get_current_timestamp()
        self.fetch_invoice_by_sub_ids = []

        # Enhanced features
        self.audit_context = audit_logger.create_audit_context(
            user_id=getattr(sclen, 'user_id', None),
            company_id=getattr(sclen, 'company_id', None)
        )

    def check_payment_status(self, rzp_sub):
        query = {'subscription_id': rzp_sub['id']}
        payment = self.db.find(DBColls.RAZORPAY_PAYMENTS, query, find_one=True)
        if not bool(payment):
            return rzp_sub

        self.sclen.utility.payment_failed = (payment['status'] == RazorpayPaymentStatus.FAILED)
        self.sclen.utility.already_subscribed = (payment['status'] == RazorpayPaymentStatus.CAPTURED)
        return rzp_sub

    def get_post_trial_start_date(self):
        now_in_secs = self.now // 1000
        trial_period = self.razorpay.saas_settings['trial_period_days']
        if not trial_period:
            raise ValueError(
                'Trial period days not available or improperly '
                'configured. Please contact support.'
            )
        trial_period_in_secs = trial_period * 86400  # seconds in a day
        start_date = now_in_secs + trial_period_in_secs
        return start_date

    @handle_payment_exception
    @with_resilience(
        circuit_breaker_name="subscription_creation",
        max_retries=3,
        failure_threshold=5,
        recovery_timeout=60
    )
    def create_subscription(self, current_plan: Dict[str, Any], curr_sub: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create subscription with enhanced error handling and validation.

        Args:
            current_plan: Current plan details
            curr_sub: Current subscription details

        Returns:
            Dict: Created subscription data

        Raises:
            PaymentValidationError: If validation fails
            PaymentBusinessLogicError: If business rules are violated
            PaymentProcessingError: If subscription creation fails
        """
        try:
            # Log subscription creation start
            self.audit_context.log_step("subscription_creation", "started")

            # Validate product ID
            try:
                product_id_str = str(curr_sub['product_id'])
            except KeyError:
                raise PaymentValidationError(
                    'Product not available or improperly configured',
                    field='product_id'
                )

            # Check trial usage
            is_trial_used = self.sclen.company.get('is_trial_used') or {}
            product_trial_used = is_trial_used.get(product_id_str, False)

            if product_trial_used and (curr_sub['status_id'] == SubscriptionStatus.TRIAL.value):
                result = self.cancel_trial_and_subscribe_new(current_plan, curr_sub)
                return result

            # Check for existing subscription
            rzp_sub = self.get_razorpay_subscription(curr_sub)
            if rzp_sub:
                self.check_payment_status(rzp_sub)
                self.sclen.utility.payment_link = rzp_sub['short_url']
                return rzp_sub

            # Validate subscription status
            valid_statuses = [SubscriptionStatus.PENDING.value, SubscriptionStatus.CANCELLED.value]
            if curr_sub['status_id'] not in valid_statuses:
                raise PaymentBusinessLogicError(
                    'Failed to subscribe. Subscription is already active/expired. Please contact support.',
                    details={
                        'current_status': curr_sub['status_id'],
                        'valid_statuses': valid_statuses
                    }
                )

            sub_data = self.get_create_subscription_data(current_plan, self.sclen.razorpay_current_plan_id)

            # Add trial period if enabled
            if self.razorpay.saas_settings['trial_period_enabled']:
                sub_data['start_at'] = self.get_post_trial_start_date()

            # Set event and addons
            sub_data['notes']['event'] = self.sclen.event
            addons = self.get_applicable_addons(curr_sub)
            if addons:
                sub_data['addons'] = addons
                sub_data = self.create_addon_references(addons, sub_data)

            # Validate and create subscription
            sub_data = self.sclen.utility.validate_payload(CreateSubscriptionPayloadValidator, sub_data)

            rzp_sub, created = self.razorpay.subscription.create_subscription(sub_data)

            if not created:
                error_code = rzp_sub.get('error', {}).get('code', 'unknown')
                error_desc = rzp_sub.get('error', {}).get('description', 'Unknown error')
                raise PaymentProcessingError(
                    f"Razorpay subscription creation failed: {error_code}: {error_desc}",
                    gateway_error=f"{error_code}: {error_desc}"
                )

            # Process and store subscription
            rzp_sub = RazorpaySubscriptionSchema(**rzp_sub).model_dump()
            self.db.insert(DBColls.RAZORPAY_SUBSCRIPTIONS, [rzp_sub])
            self.sclen.utility.payment_link = rzp_sub['short_url']
            rzp_sub.pop('_id', None)

            # Update subscription with Razorpay ID
            query = {'id': curr_sub['id']}
            update_query = {'razorpay_sub_id': rzp_sub['id']}
            self.db.update(DBColls.SUBSCRIPTIONS, query, update_query)

            # Schedule invoice fetching
            self.fetch_invoice_by_sub_ids.append(rzp_sub['id'])

            self.audit_context.log_step("subscription_creation", "completed")
            return rzp_sub

        except Exception as e:
            self.audit_context.log_step("subscription_creation", "failed", {
                'error_type': type(e).__name__,
                'error_message': str(e)
            })
            raise

    def check_if_switched_to_new(self, curr_sub):
        rzp_sub = self.get_razorpay_subscription(curr_sub)
        notes = rzp_sub.get('notes') or {}
        cancelled_sub_id = bool(notes.get('cancelled_sub_id'))
        switched_to_new = (notes.get('event') == SubscriptionEvent.SUBSCRIBE)
        return (cancelled_sub_id and switched_to_new), rzp_sub

    def cancel_trial_and_subscribe_new(self, current_plan, curr_sub):
        switched_to_new, rzp_sub = self.check_if_switched_to_new(curr_sub)
        if switched_to_new:
            self.check_payment_status(rzp_sub)
            self.sclen.utility.payment_link = rzp_sub['short_url']
            return rzp_sub

        sub_data = self.get_create_subscription_data(current_plan, self.sclen.razorpay_current_plan_id)
        sub_data['notes']['event'] = self.sclen.event
        addons = self.get_applicable_addons(curr_sub)
        if addons:
            sub_data['addons'] = addons
            sub_data = self.create_addon_references(addons, sub_data)

        # keeping the reference of cancelled subscription during trial to new
        # subscribe within trial period (technically upgrade within trial period)
        # for generating cancelled subscription's invoice later
        current_razorpay_sub_id = curr_sub.get('razorpay_sub_id')
        sub_data['notes']['cancelled_sub_id'] = current_razorpay_sub_id

        sub_data = self.sclen.utility.validate_payload(CreateSubscriptionPayloadValidator, sub_data)
        rzp_sub, created = self.razorpay.subscription.create_subscription(sub_data)
        if not created:
            error_code = rzp_sub['error']['code']
            error_desc = rzp_sub['error']['description']
            raise ValueError(f"{error_code}: {error_desc}")

        rzp_sub = RazorpaySubscriptionSchema(**rzp_sub).model_dump()
        self.db.insert(DBColls.RAZORPAY_SUBSCRIPTIONS, [rzp_sub])
        self.sclen.utility.payment_link = rzp_sub['short_url']
        rzp_sub.pop('_id', None)

        query = {'id': curr_sub['id']}
        update_query = {'razorpay_sub_id': rzp_sub['id']}
        self.db.update(DBColls.SUBSCRIPTIONS, query, update_query)

        # fetching invoice for subscription
        self.fetch_invoice_by_sub_ids.append(rzp_sub['id'])

        # cancelling current trial subscription
        self.razorpay.subscription.cancel_subscription(current_razorpay_sub_id)

        return rzp_sub

    def upgrade_subscription(self, new_plan, curr_sub):
        current_razorpay_sub_id = curr_sub.get('razorpay_sub_id')
        if not current_razorpay_sub_id:
            raise ValueError(
                'Upgrade failed. Subscription does not '
                'exist or improperly configured. Please '
                'contact support.'
            )

        if (curr_sub['status_id'] not in [SubscriptionStatus.TRIAL.value, SubscriptionStatus.ACTIVE.value]):
            raise ValueError(
                'Upgrade failed. Current subscription is '
                'not active. Please contact support.'
            )

        if (curr_sub['product_id'] != new_plan['product_id']):
            raise ValueError(
                "Upgrade failed. Mismatch in the selected plan's "
                "vs current plan's product module. Please contact "
                "support."
            )

        rzp_sub = self.cancel_and_upgrade(new_plan, curr_sub, current_razorpay_sub_id)
        return rzp_sub

    def cancel_and_upgrade(self, new_plan, curr_sub, current_razorpay_sub_id):
        curr_rzp_sub = self.get_razorpay_subscription(curr_sub)
        if not curr_rzp_sub:
            raise ValueError('Current plan unavailable or improperly configured. Please contact support.')

        razorpay_addons = self.get_applicable_addons(new_plan)
        sub_data = self.get_create_subscription_data(new_plan, self.sclen.razorpay_new_plan_id)
        sub_data = self.create_addon_references(razorpay_addons, sub_data)
        sub_data['total_count'] = curr_rzp_sub['remaining_count']
        sub_data['start_at'] = curr_rzp_sub['current_end']

        if (curr_sub['status_id'] != SubscriptionStatus.TRIAL.value):
            if (curr_rzp_sub['status'] != SubscriptionStatus.ACTIVE.name.lower()):
                raise ValueError(
                    'Current plan is inactive. Please '
                    'activate the plan or contact support.'
                )

            upgrade_addon = self.calculate_prorated_amount(new_plan, curr_sub, curr_rzp_sub)
            if upgrade_addon:
                razorpay_addons.append(upgrade_addon)
                self.sclen.is_prorated = True

        if razorpay_addons:
            sub_data['addons'] = razorpay_addons

        sub_data['notes']['event'] = self.sclen.event

        # keeping the reference of cancelled subscription during upgrade for generating invoice later
        sub_data['notes']['cancelled_sub_id'] = current_razorpay_sub_id

        sub_data = self.sclen.utility.validate_payload(CreateSubscriptionPayloadValidator, sub_data)
        rzp_sub, created = self.razorpay.subscription.create_subscription(sub_data)
        if not created:
            error_code = rzp_sub['error']['code']
            error_desc = rzp_sub['error']['description']
            raise ValueError(f"{error_code}: {error_desc}")

        rzp_sub = RazorpaySubscriptionSchema(**rzp_sub).model_dump()
        self.db.insert(DBColls.RAZORPAY_SUBSCRIPTIONS, [rzp_sub])
        self.sclen.utility.payment_link = rzp_sub['short_url']
        rzp_sub.pop('_id', None)

        rzp_sub = self.finalise_upgrade(new_plan, curr_sub, rzp_sub)

        # cancelling current subscription
        self.razorpay.subscription.cancel_subscription(current_razorpay_sub_id)
        return rzp_sub

    def finalise_upgrade(self, new_plan, curr_sub, rzp_sub):
        update_query = {
            'plan_id': new_plan['id'],
            'plan_name': new_plan['plan_name'],
            'price': new_plan['price'],
            'pricing_period': new_plan['pricing_period'],
            'annual_discount': new_plan['annual_discount'],
            'razorpay_sub_id': rzp_sub['id'],
            'billing_cycle': new_plan['billing_cycle'],
            'billing_cycle_label': new_plan['billing_cycle_label']
        }

        curr_addons = curr_sub.get('addons', [])
        curr_addon_ids = [cs_a['id'] for cs_a in curr_addons]
        new_addons = [np_a for np_a in new_plan.get('addons', []) if np_a['id'] not in curr_addon_ids]
        curr_addons += new_addons
        update_query['addons'] = curr_addons

        query = {'id': curr_sub['id']}
        self.db.update(DBColls.SUBSCRIPTIONS, query, update_query)
        self.fetch_invoice_by_sub_ids.append(rzp_sub['id'])
        return rzp_sub

    def create_addon_references(self, addons, sub_data):
        features = {addon['item']['name']: addon['item']['description'] for addon in addons}
        if features:
            sub_data['notes'].update(features)
        return sub_data

    def get_create_subscription_data(self, plan, razorpay_plan_id):
        total_count = self.sclen._get_billing_cycles(plan)
        expire_by = convert_to_unix_secs(
            add_days(self.now, SUBSCRIPTION_LINK_EXPIRY)
        )

        subscription_data = self.razorpay.subscription.get_subscription_schema()
        subscription_data.update({
            'plan_id': razorpay_plan_id,
            'total_count': total_count,
            'expire_by': expire_by
        })
        subscription_data['notes'].update({
            'user_id': self.sclen.user_id,
            'company_id': self.sclen.company_id,
            'subscription_id': self.sclen.current_subscription_id
        })
        # subscription_data['notify_info'].update({
        #     'notify_phone': self._process_notify_phone(),
        #     'notify_email': self.sclen.company.get('email')
        # })
        return subscription_data

    def get_razorpay_subscription(self, subscription):
        razorpay_sub_id = subscription.get('razorpay_sub_id')
        if not razorpay_sub_id:
            return None

        query = {'id': razorpay_sub_id}
        return self.db.find(DBColls.RAZORPAY_SUBSCRIPTIONS, query, find_one=True)

    def get_billing_cycle(self, entity):
        billing_cycle = entity['billing_cycle']
        return billing_cycle, (billing_cycle == BillingCycle.ANNUALLY)

    def get_applicable_addons(self, entity):
        addons = self.sclen._process_addons(entity)
        return addons

    def calculate_prorated_amount(self, new_plan, curr_sub, curr_rzp_sub):
        _, new_plan_is_yearly = self.get_billing_cycle(new_plan)
        _, current_plan_is_yearly = self.get_billing_cycle(curr_sub)
        if current_plan_is_yearly and not new_plan_is_yearly:
            # [Yearly -> Monthly Upgrade Not Applicable]
            # If we want to enable this, we need to
            # introduce REFUND flows. So keeping it disabled.
            raise ValueError(
                'Yearly -> Monthly updation of a plan is not '
                'available in the pricing at the moment. We '
                'are working on it. Sorry for the inconvenience. '
                'If you have any queries or suggestions, please '
                'contact our support team.'
            )

        unused_amount = self.get_unused_amount(curr_sub, curr_rzp_sub)
        # new_plan_amount = self.sclen.razorpay_new_plan.get('item', {}).get('amount') or 0
        # new_plan_amount = (new_plan_amount / 100)  # in rupees
        # if (new_plan_amount == 0):
        #     raise ValueError('Upgrade failed; plan amount cannot be zero.')

        upgrade_amount = (new_plan['price'] - unused_amount)
        upgrade_amount = self.sclen.compute_tax(
            taxable_amount=upgrade_amount, gst_details=False
        )

        if (upgrade_amount <= 0):
            return None

        upgrade_addon = self.razorpay.subscription.get_subscription_addon_schema()
        upgrade_addon['item'].update({
            'name': self._get_addon_name(new_plan),
            'amount': self.sclen._process_amount(upgrade_amount),
            'currency': new_plan.get('currency', Currency.INR),
            'description': self._get_addon_description(new_plan)
        })
        return upgrade_addon

    def get_unused_amount(self, curr_sub, curr_rzp_sub):
        upgrade_date = DateUtil.convert_from_utc(self.now)  # In IST
        upgrade_date = DateUtil.convert_to_datetime(upgrade_date)  # in ms
        if upgrade_date.hour <= 12:
            upgrade_date = upgrade_date.replace(
                hour=0, minute=0, second=0, microsecond=0)

        current_start = curr_rzp_sub['current_start'] * 1000  # in ms
        current_start = DateUtil.convert_from_utc(current_start) # in IST
        current_start_date = DateUtil.convert_to_datetime(current_start)

        current_end = curr_rzp_sub['current_end'] * 1000  # in ms
        current_end = DateUtil.convert_from_utc(current_end)  # in IST
        current_end_date = DateUtil.convert_to_datetime(current_end)

        remaining_days = (current_end_date - upgrade_date).days
        total_days = (current_end_date - current_start_date).days
        total_days = total_days if (total_days >= 30) else 30

        # current_plan_amount =  self.sclen.razorpay_current_plan.get('item', {}).get('amount') or 0
        # current_plan_amount = (current_plan_amount / 100)  # in rupees
        # if (current_plan_amount == 0):
        #     raise ValueError('Upgrade failed; plan amount cannot be zero.')

        unused_amount = curr_sub['price'] * (remaining_days / total_days)
        return unused_amount

    def _get_addon_description(self, plan):
        return (
            f"Upgrading to {plan['plan_name']} with "
            "prorated charges for the remaining billing "
            "period."
        )

    def _get_addon_name(self, plan):
        name = plan['plan_name']
        product = plan['product_name']
        return f"{product} - {name} (Prorated charges)"

    def _process_notify_phone(self, c_code='91'):
        phone = str(self.sclen.company.get('phone') or '')
        if (len(phone) < 10) or (phone == ''):
            return ''

        phone = phone[-10:]
        # '+' not required, razorpay prepends it automatically.
        phone = ''.join((c_code, phone))
        return phone

    def fetch_and_create_invoices(self):
        query = {'subscription_id': {'$in': self.fetch_invoice_by_sub_ids}}
        fetched_invoice_sub_ids = self.db.distinct(DBColls.RAZORPAY_INVOICES, 'subscription_id', query)
        for sub_id in self.fetch_invoice_by_sub_ids:
            if sub_id not in fetched_invoice_sub_ids:
                try:
                    subscription_invoices, fetched = self.razorpay.invoice.get_invoices(sub_id=sub_id)
                except Exception as e:
                    logger.info(f'Error while fetching invoices by subscription_id {sub_id}: {e}')
                    continue

                if not fetched:
                    error_code = subscription_invoices['error']['code']
                    error_desc = subscription_invoices['error']['description']
                    logger.info(f'Error while fetching invoices by subscription_id {sub_id}: {error_code} - {error_desc}')
                    continue

                try:
                    invoices = subscription_invoices['items']
                except KeyError:
                    invoices = [subscription_invoices]

                for invoice in invoices:
                    invoice['invoice_number'] = generate_sequence_no(SequenceType.INVOICE_NUMBER)

                if invoices:
                    self.db.insert(DBColls.RAZORPAY_INVOICES, invoices)