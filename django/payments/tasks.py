import logging
from celery import shared_task
from django.conf import settings
from config.celery_conf import load_dynamic_schedule

logger = logging.getLogger('celery')


# @app.task
@shared_task
def refresh_celery_schedule():
    # app.conf.beat_schedule = load_dynamic_schedule()
    settings.CELERY_BEAT_SCHEDULE = load_dynamic_schedule()
    return "Celery Beat Schedule Updated!"


@shared_task(bind=True, ignore_result=True)
def pre_debit_notification(self):
    from scripts.pre_debit_notification import run
    return run()


@shared_task(bind=True, ignore_result=True)
def autopay_revoked_during_trial(self):
    from scripts.handle_autopay_revoked_during_trial import run
    return run()
