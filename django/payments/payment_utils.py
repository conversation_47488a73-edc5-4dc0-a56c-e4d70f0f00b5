import os
import logging
from datetime import timedelta
from django.conf import settings 
from django.template.loader import render_to_string
from utils.constants import (
    SMSTemplateId,
    PRECISSION,
)
from utils import (
    send_email_with_pdf,
    DummyUtility,
    DummyClass,
    DateUtil,
    send_sms
)

logger = logging.getLogger('application')


def get_dummy_utility(dict_obj={}):
    fake_request = DummyClass(dict_obj)
    utility = DummyUtility(fake_request)
    return utility


def add_days(timestamp, days):
    date_obj = DateUtil.convert_to_datetime(timestamp)
    return date_obj + timedelta(days=days)


def convert_to_unix_secs(date_obj):
    timestamp_in_ms = DateUtil.convert_to_unix(date_obj)
    timestamp_in_secs = timestamp_in_ms // 1000
    return timestamp_in_secs


def convert_to_ms(unix):
    if unix is None:
        return unix

    return unix * 1000


def back_compute_tax(amount):
    '''
        x + (18 % of x) = amount (in rupees)
        x * (0.18 + 1) = amount (in rupees)
        x = (amount / 1.18)

        1.18 => factored out tax value
        x => taxable amount
    '''

    tax_details = {}
    service_tax = settings.SERVICE_TAX_PERC  # service tax
    tax_perc = round(service_tax / 100, PRECISSION)
    factored_tax = (tax_perc + 1)

    taxable_amount = (amount / factored_tax)
    taxable_amount = round(taxable_amount, PRECISSION)
    tax_details['taxable_amount'] = taxable_amount * 100
    tax_details['taxable_amount_in_rupees'] = taxable_amount

    tax_amount = (amount - taxable_amount)
    tax_amount = round(tax_amount, PRECISSION)
    tax_details['tax_amount'] = tax_amount * 100
    tax_details['tax_amount_in_rupees'] = tax_amount
    return tax_details


def compute_gst_details(tax_details, customer_state_code):
    gst_details = {
        'igst': 0,
        'igst_in_rupees': 0,
        'igst_percent': 0,
        'cgst': 0,
        'cgst_in_rupees': 0,
        'cgst_percent': 0,
        'sgst': 0,
        'sgst_in_rupees': 0,
        'sgst_percent': 0
    }
    host_state_code = settings.HOST_STATE_CODE
    service_tax_perc = settings.SERVICE_TAX_PERC
    if (host_state_code != customer_state_code) or (not customer_state_code):
        gst_details.update({
            'igst': tax_details['tax_amount'],
            'igst_in_rupees': tax_details['tax_amount_in_rupees'],
            'igst_percent': service_tax_perc
        })
    else:
        splt_tax_amount = float(tax_details['tax_amount_in_rupees'] / 2.0)
        splt_tax_amount = round(splt_tax_amount, PRECISSION)
        split_tax_percent = float(service_tax_perc / 2.0)
        split_tax_percent = round(split_tax_percent, PRECISSION)
        gst_details.update({
            'cgst': splt_tax_amount * 100,
            'cgst_in_rupees': splt_tax_amount,
            'cgst_percent': split_tax_percent,
            'sgst': splt_tax_amount * 100,
            'sgst_in_rupees': splt_tax_amount,
            'sgst_percent': split_tax_percent
        })

    tax_details.update(gst_details)
    return tax_details


class Finder(object):
    def __init__(self):
        self.changes = {}
        self.push_query = {}

    def _is_empty(self, value):
        if isinstance(value, dict):
            return not bool(value)
        if isinstance(value, list):
            return not bool(value)
        return value is None

    def _process_primitive(self, path, payload_val, existing_val):
        if not self._is_empty(payload_val) and payload_val != existing_val:
            self.changes[path] = payload_val

    def _process_list(self, path, payload_list, existing_list, stack):
        min_len = min(len(payload_list), len(existing_list))
        for idx in range(min_len):
            incoming_item = payload_list[idx]
            if self._is_empty(incoming_item):
                continue

            existing_item = existing_list[idx]
            indexed_path = f"{path}.{idx}" if path else str(idx)
            stack.append((indexed_path, incoming_item, existing_item))

        if len(payload_list) == len(existing_list):
            return

        if len(payload_list) > len(existing_list):
            self.push_query = {path: {'$each': payload_list[min_len:]}}
        else:
            self.push_query = {path: {'$each': existing_list[min_len:]}}

    def _process_dict(self, path, payload_part, existing_part, stack):
        for key in payload_part:
            current_key_path = f"{path}.{key}" if path else key
            if key not in existing_part:
                # if not self._is_empty(payload_part[key]):
                self.changes[current_key_path] = payload_part[key]
                continue

            payload_val = payload_part[key]
            existing_val = existing_part[key]

            if isinstance(payload_val, dict) and isinstance(existing_val, dict):
                stack.append((current_key_path, payload_val, existing_val))
            elif isinstance(payload_val, list) and isinstance(existing_val, list):
                self._process_list(
                    current_key_path,
                    payload_val,
                    existing_val,
                    stack
                )
            else:
                if not self._is_empty(payload_val) and payload_val != existing_val:
                    self.changes[current_key_path] = payload_val

    def track_changes(self, payload, existing_doc):
        if not (payload and existing_doc):
            return

        stack = [("", payload, existing_doc)]
        while stack:
            path, payload_part, existing_part = stack.pop()

            if isinstance(payload_part, dict) and isinstance(existing_part, dict):
                self._process_dict(
                    path,
                    payload_part,
                    existing_part,
                    stack
                )
            elif isinstance(payload_part, list) and isinstance(existing_part, list):
                self._process_list(
                    path,
                    payload_part,
                    existing_part,
                    stack
                )
            else:
                self._process_primitive(
                    path,
                    payload_part,
                    existing_part
                )

    def _get_update_query(self):
        return self.changes

    def _get_push_query(self):
        return self.push_query


def send_payment_sms(data, company, user):
    amnt_in_rs = data['amount_in_rupees']
    amnt_in_rs = int(amnt_in_rs) if (amnt_in_rs == int(amnt_in_rs)) else amnt_in_rs
    amount = amnt_in_rs
    message_params = [amount]
    message = f"Your payment of Rs.{amount} for the subscription to SCLEN.AI is successful. Any recurring subscription payments will be automatically charged"
    params = {
        'receivers': [company['phone'], user['phone']],
        'message': message,
        'template_id': SMSTemplateId.MCP_SMS_TXN,
        'user_details': {
            'company_id': company['id'],
            'company_name': company['name']
        },
        'message_params': message_params,
        'alert_type': 'Subscription',
        'module_id': 2
    }
    send_sms(params)


def send_invoice_to_email(invoice, email_subject, email_body, email_body_context):
    css_file = "css/invoice.css"
    template_name = "invoice.html"
    pdf_filename= f"{invoice['invoice_number']}.pdf"

    html_content = render_to_string(email_body, email_body_context)
    css_path = os.path.join(settings.STATIC_ROOT, css_file)
    pdf_content = render_to_string(template_name, invoice)

    customer_details = invoice['customer_details']
    customer_email = customer_details['customer_email']
    user_email = customer_details['email']

    send_email_with_pdf(
        customer_email,
        [user_email],
        email_subject,
        html_content,
        pdf_content,
        css_path,
        pdf_filename=pdf_filename
    )