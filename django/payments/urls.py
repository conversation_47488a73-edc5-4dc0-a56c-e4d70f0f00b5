from django.urls import path
from .views import (
    test_invoice,
    PaymentsListingApiView,
    PaygListingApiView,
    PaymentLinkApiView,
    PaymentLinkPaygApiView,
    PaymentCompleteApiView,
    PaymentCompletePaygApiView,
    PaymentInvoiceDownloadApiView,
    SubscriptionPauseApiView,
    SubscriptionResumeApiView,
    SubscriptionCancelApiView,
    ComputeTaxApiView

)
from .webhook_views import WebhookApiView

urlpatterns = [
    path('invoice/', test_invoice, name='invoice'),
    path('listing/', PaymentsListingApiView.as_view(), name='payments_listing'),
    path('payg/listing/', PaygListingApiView.as_view(), name='payment_link_payg'),
    path('payment-link/', PaymentLinkApiView.as_view(), name='payment_link'),
    path('payment-link/payg/', PaymentLinkPaygApiView.as_view(), name='payment_link_payg'),
    path('payment-link/payg/<str:order_id>/', PaymentLinkPaygApiView.as_view(), name='payment_link_payg'),
    path('payment-complete/<str:subscription_id>/', PaymentCompleteApiView.as_view(), name='payment_complete'),
    path('payment-complete/payg/<str:order_id>/', PaymentCompletePaygApiView.as_view(), name='payment_complete_payg'),
    path('invoice/<str:invoice_id>/download/', PaymentInvoiceDownloadApiView.as_view(), name='payment_invoice_download'),
    path('subscription/<str:subscription_id>/pause/', SubscriptionPauseApiView.as_view(), name='pause_subscription'),
    path('subscription/<str:subscription_id>/resume/', SubscriptionResumeApiView.as_view(), name='resume_subscription'),
    path('subscription/<str:subscription_id>/cancel/', SubscriptionCancelApiView.as_view(), name='cancel_subscription'),
    path('tax/', ComputeTaxApiView.as_view(), name='compute_tax'),

    # Webhook URLs
    path('h/s/wh/', WebhookApiView.as_view(), name='handle_subscription_webhook'),
    path('h/i/wh/', WebhookApiView.as_view(), name='handle_invoice_webhook'),
    path('h/o/wh/', WebhookApiView.as_view(), name='handle_order_webhook'),
    path('h/p/wh/', WebhookApiView.as_view(), name='handle_payment_webhook')
]