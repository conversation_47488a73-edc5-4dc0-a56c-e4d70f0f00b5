from django.conf import settings

REMINDER_ENABLE = getattr(settings, 'REMINDER_ENABLE', True)
NOTIFY_PLINK_VIA_SMS = getattr(settings, 'NOTIFY_PLINK_VIA_SMS', False)
NOTIFY_PLINK_VIA_EMAIL = getattr(settings, 'NOTIFY_PLINK_VIA_EMAIL', False)
NOTIFY_PLINK_VIA_WHATSAPP = getattr(settings, 'NOTIFY_PLINK_VIA_WHATSAPP', False)

NOTIFY_INVOICE_VIA_SMS = getattr(settings, 'NOTIFY_INVOICE_VIA_SMS', True)
NOTIFY_INVOICE_VIA_EMAIL = getattr(settings, 'NOTIFY_INVOICE_VIA_EMAIL', True)

SUBSCRIPTION_LINK_EXPIRY = getattr(settings, 'SUBSCRIPTION_LINK_EXPIRY', 90)  # days
PAYMENT_LINK_EXPIRY = getattr(settings, 'PAYMENT_LINK_EXPIRY', 90)  # days