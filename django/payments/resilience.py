import time
import logging
import random
from typing import Callable, Any
from enum import Enum
from functools import wraps
from .exceptions import PaymentRetryableError, PaymentSystemError
from utils import RedisUtils

logger = logging.getLogger('application')


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, rejecting requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class RetryStrategy(Enum):
    """Retry strategy types."""
    FIXED = "fixed"
    EXPONENTIAL = "exponential"
    LINEAR = "linear"


class CircuitBreaker:
    """
    Circuit breaker implementation for payment gateway calls.

    Prevents cascading failures by monitoring failure rates and
    temporarily blocking requests when failure threshold is exceeded.
    """

    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception
    ):
        self.name = name
        self.cache = RedisUtils()
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.cache_prefix = f"circuit_breaker:{name}"

    def _get_state(self) -> CircuitBreakerState:
        """Get current circuit breaker state."""
        state_data = self.cache.get_data(f"{self.cache_prefix}:state")
        if not state_data:
            state_data = {
                'state': CircuitBreakerState.CLOSED.value,
                'failure_count': 0,
                'last_failure_time': 0
            }
        return CircuitBreakerState(state_data['state'])

    def _get_failure_count(self) -> int:
        """Get current failure count."""
        state_data = self.cache.get_data(f"{self.cache_prefix}:state") or {'failure_count': 0}
        return state_data['failure_count']

    def _increment_failure_count(self):
        """Increment failure count and update state if needed."""
        state_data = self.cache.get_data(f"{self.cache_prefix}:state")
        if not state_data:
            state_data = {
                'state': CircuitBreakerState.CLOSED.value,
                'failure_count': 0,
                'last_failure_time': 0
            }

        state_data['failure_count'] += 1
        state_data['last_failure_time'] = time.time()

        # Open circuit if threshold exceeded
        if state_data['failure_count'] >= self.failure_threshold:
            state_data['state'] = CircuitBreakerState.OPEN.value
            logger.warning(f"Circuit breaker {self.name} opened due to {state_data['failure_count']} failures")

        self.cache.set_data(f"{self.cache_prefix}:state", state_data, self.recovery_timeout * 2)

    def _reset_failure_count(self):
        """Reset failure count and close circuit."""
        state_data = {
            'state': CircuitBreakerState.CLOSED.value,
            'failure_count': 0,
            'last_failure_time': 0
        }
        self.cache.set_data(f"{self.cache_prefix}:state", state_data, self.recovery_timeout * 2)
        logger.info(f"Circuit breaker {self.name} reset to closed state")

    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit."""
        state_data = self.cache.get_data(f"{self.cache_prefix}:state") or {'last_failure_time': 0}
        return (time.time() - state_data['last_failure_time']) >= self.recovery_timeout

    def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.

        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Function result

        Raises:
            PaymentSystemError: If circuit is open
        """
        state = self._get_state()

        # If circuit is open, check if we should try to recover
        if state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                # Move to half-open state
                state_data = self.cache.get_data(f"{self.cache_prefix}:state") or {}
                state_data['state'] = CircuitBreakerState.HALF_OPEN.value
                self.cache.set_data(f"{self.cache_prefix}:state", state_data, self.recovery_timeout * 2)
                logger.info(f"Circuit breaker {self.name} moved to half-open state")
            else:
                raise PaymentSystemError(
                    f"Circuit breaker {self.name} is open",
                    details={
                        'circuit_breaker': self.name,
                        'state': state.value,
                        'failure_count': self._get_failure_count()
                    }
                )

        try:
            result = func(*args, **kwargs)

            # Success - reset circuit if it was half-open
            if state == CircuitBreakerState.HALF_OPEN:
                self._reset_failure_count()

            return result

        except self.expected_exception as e:
            self._increment_failure_count()
            raise


class RetryManager:
    """Manages retry logic for payment operations."""

    def __init__(
        self,
        max_attempts: int = 3,
        strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        jitter: bool = True
    ):
        self.max_attempts = max_attempts
        self.strategy = strategy
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.jitter = jitter

    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt."""
        if self.strategy == RetryStrategy.FIXED:
            delay = self.base_delay
        elif self.strategy == RetryStrategy.LINEAR:
            delay = self.base_delay * attempt
        else:  # EXPONENTIAL
            delay = self.base_delay * (2 ** (attempt - 1))

        # Apply maximum delay limit
        delay = min(delay, self.max_delay)

        # Add jitter to prevent thundering herd
        if self.jitter:
            delay += random.uniform(0, delay * 0.1)

        return delay

    def execute_with_retry(
        self,
        func: Callable,
        *args,
        retryable_exceptions: tuple = (PaymentRetryableError,),
        **kwargs
    ) -> Any:
        """
        Execute function with retry logic.

        Args:
            func: Function to execute
            *args: Function arguments
            retryable_exceptions: Exceptions that should trigger retry
            **kwargs: Function keyword arguments

        Returns:
            Function result

        Raises:
            Last exception if all retries exhausted
        """
        last_exception = None

        for attempt in range(1, self.max_attempts + 1):
            try:
                logger.debug(f"Executing {func.__name__}, attempt {attempt}/{self.max_attempts}")
                return func(*args, **kwargs)

            except retryable_exceptions as e:
                last_exception = e

                if attempt == self.max_attempts:
                    logger.error(f"All retry attempts exhausted for {func.__name__}")
                    break

                delay = self._calculate_delay(attempt)
                logger.warning(
                    f"Attempt {attempt} failed for {func.__name__}, retrying in {delay:.2f}s: {str(e)}"
                )
                time.sleep(delay)

            except Exception as e:
                # Non-retryable exception
                logger.error(f"Non-retryable exception in {func.__name__}: {str(e)}")
                raise

        # All retries exhausted
        raise last_exception


def with_circuit_breaker(
    name: str,
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    expected_exception: type = Exception
):
    """
    Decorator to add circuit breaker protection to functions.

    Args:
        name: Circuit breaker name
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Time to wait before attempting recovery
        expected_exception: Exception type that triggers circuit breaker
    """
    def decorator(func):
        circuit_breaker = CircuitBreaker(
            name=name,
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            expected_exception=expected_exception
        )

        @wraps(func)
        def wrapper(*args, **kwargs):
            return circuit_breaker.call(func, *args, **kwargs)

        return wrapper
    return decorator


def with_retry(
    max_attempts: int = 3,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    retryable_exceptions: tuple = (PaymentRetryableError,)
):
    """
    Decorator to add retry logic to functions.

    Args:
        max_attempts: Maximum number of retry attempts
        strategy: Retry strategy to use
        base_delay: Base delay between retries
        max_delay: Maximum delay between retries
        retryable_exceptions: Exceptions that should trigger retry
    """
    def decorator(func):
        retry_manager = RetryManager(
            max_attempts=max_attempts,
            strategy=strategy,
            base_delay=base_delay,
            max_delay=max_delay
        )

        @wraps(func)
        def wrapper(*args, **kwargs):
            return retry_manager.execute_with_retry(
                func, *args, retryable_exceptions=retryable_exceptions, **kwargs
            )

        return wrapper
    return decorator


def with_resilience(
    circuit_breaker_name: str,
    max_retries: int = 3,
    failure_threshold: int = 5,
    recovery_timeout: int = 60
):
    """
    Decorator combining circuit breaker and retry patterns.

    Args:
        circuit_breaker_name: Name for the circuit breaker
        max_retries: Maximum retry attempts
        failure_threshold: Circuit breaker failure threshold
        recovery_timeout: Circuit breaker recovery timeout
    """
    def decorator(func):
        # Apply circuit breaker first, then retry
        func = with_circuit_breaker(
            name=circuit_breaker_name,
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            expected_exception=Exception
        )(func)

        func = with_retry(
            max_attempts=max_retries,
            retryable_exceptions=(PaymentRetryableError, PaymentSystemError)
        )(func)

        return func
    return decorator
