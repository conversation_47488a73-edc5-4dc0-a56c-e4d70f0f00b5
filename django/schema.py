import logging
from pydantic import (
    PositiveInt,
    BaseModel,
    EmailStr,
    Field
)
from typing import Union, Optional
from utils.common import get_uuid
from utils.constants import (
    BillingCycle,
    Currency
)

logger = logging.getLogger('application')


class CreateRazorpayPlanNoteSchema(BaseModel):
    plan_id: str = ""


class CreateRazorpayPlanItem(BaseModel):
    name: str = ""
    amount: PositiveInt | None = 0
    currency: str = Currency.INR
    description: str = ""


class CreateRazorpayPlanSchema(BaseModel):
    period: str | None = ''
    interval: int = 1
    item: CreateRazorpayPlanItem = Field(default_factory=CreateRazorpayPlanItem)
    notes: CreateRazorpayPlanNoteSchema = Field(default_factory=CreateRazorpayPlanNoteSchema)


class CreateRazorpayCustomerNoteSchema(BaseModel):
    pan: str = ""
    address: str = ""
    state: str = ""
    city: str = ""
    pincode: str = ""
    company_type: int = 0
    company_id: str = ""


class CreateRazorpayCustomerSchema(BaseModel):
    name: str = ""
    email: EmailStr = ""
    fail_existing: int = 1
    contact: str = ""
    gstin: str = ""
    notes: CreateRazorpayCustomerNoteSchema = Field(
        default_factory=CreateRazorpayCustomerNoteSchema)


class CreateRazorpayAddonItemSchema(BaseModel):
    name: str = ""
    amount: int = 0
    currency: str = Currency.INR
    description: str = ""


class CreateRazorpaySubscriptionAddonSchema(BaseModel):
    item: CreateRazorpayAddonItemSchema = Field(
        default_factory=CreateRazorpayAddonItemSchema)
    quantity: int = 1


class CreateRazorpaySubscriptionNoteSchema(BaseModel):
    user_id: str = ""
    company_id: str = ""
    subscription_id: str = ""


class CreateRazorpaySubscriptionNotifyInfoSchema(BaseModel):
    notify_phone: str = ''
    notify_email: EmailStr = ''


class CreateRazorpaySubscriptionSchema(BaseModel):
    plan_id: str = ""
    customer_notify: int = 0
    quantity: int = 1
    total_count: int = 1
    expire_by: int = 0
    addons: list[CreateRazorpaySubscriptionAddonSchema] = Field(default_factory=list)
    notes: CreateRazorpaySubscriptionNoteSchema = Field(
        default_factory=CreateRazorpaySubscriptionNoteSchema)


class CreateRazorpayOrderNoteSchema(BaseModel):
    user_id: str | None = ''
    company_id: str | None = ''
    subscription_id: str | None = ''

    class Config:
        extra = 'allow'


class CreateRazorpayOrderSchema(BaseModel):
    amount: PositiveInt | None = 0
    currency: str = Currency.INR
    receipt: str = ""
    notes: CreateRazorpayOrderNoteSchema = Field(
        default_factory=CreateRazorpayOrderNoteSchema)


class CreatePaymentLinkNotifySchema(BaseModel):
    sms: bool = False
    email: bool = False
    whatsapp: bool = False


class CreatePaymentLinkNoteSchema(BaseModel):
    current_plan: str = ''
    current_plan_id: str = ''
    current_subscription_id: str = ''
    billing_cycle: str = BillingCycle.ONE_TIME
    for_entity: str = Field(default='order')
    validity: str = 'lifetime'
    prorated: bool = False


class CreatePaymentLinkSchema(BaseModel):
    amount: int = 0
    currency: str = Currency.INR
    customer_id: str = ""
    description: str = ""
    reference_id: str = ""
    expire_by: int = 0
    notify: CreatePaymentLinkNotifySchema = Field(
        default_factory=CreatePaymentLinkNotifySchema)
    reminder_enable: bool = False
    notes: CreatePaymentLinkNoteSchema = Field(
        default_factory=CreatePaymentLinkNoteSchema)
    callback_url: str = ""
    callback_method: str = "get"


class RazorpayCustomerNoteSchema(BaseModel):
    address: str = ""
    city: str = ""
    company_id: str = ""
    company_type: int = 0
    pan: str = ""
    pincode: str = ""
    state: str = ""


class RazorpayCustomerSchema(BaseModel):
    id: str
    name: str
    entity: str
    email: str
    contact: str
    gstin: Union[str, None]
    created_at: Union[int, None]
    notes: Union[RazorpayCustomerNoteSchema, list] = Field(
        default_factory=RazorpayCustomerNoteSchema)
    shipping_address: list = []


class RazorpayPlanNoteSchema(BaseModel):
    plan_id: str = ""

    class Config:
        extra = 'allow'


class RazorpayPlanItemSchema(BaseModel):
    id: str
    active: bool
    name: str
    amount: PositiveInt
    unit_amount: PositiveInt
    currency: str
    description: str
    type: str
    unit: Union[int, None]
    tax_inclusive: bool = False
    hsn_code: Union[int, None]
    sac_code: Union[int, None]
    tax_rate: Union[int, None]
    tax_id: Union[str, None]
    tax_group_id: Union[str, None]
    created_at: Union[int, None]
    updated_at: Union[int, None]


class RazorpayPlanSchema(BaseModel):
    id: str
    entity: str = 'plan'
    interval: int
    period: str = 'monthly'
    item: RazorpayPlanItemSchema
    notes: RazorpayPlanNoteSchema = Field(default_factory=RazorpayPlanNoteSchema)
    created_at: Union[int, None]


class RazorpaySubscriptionNoteSchema(BaseModel):
    user_id: str
    company_id: str
    subscription_id: str

    class Config:
        extra = "allow"


class RazorpaySubscriptionSchema(BaseModel):
    id: str
    entity: str
    plan_id: str
    status: str
    current_start: Union[int, None]
    current_end: Union[int, None]
    ended_at: Union[int, None]
    quantity: int
    notes: RazorpaySubscriptionNoteSchema = Field(
        default_factory=RazorpaySubscriptionNoteSchema)
    charge_at: Union[int, None]
    start_at: Union[int, None]
    end_at: Union[int, None]
    auth_attempts: int
    total_count: int
    paid_count: int
    customer_notify: bool = False
    created_at: Union[int, None]
    expire_by: Union[int, None]
    short_url: str
    has_scheduled_changes: bool = False
    change_scheduled_at: Union[int, None]
    source: str
    remaining_count: int


class RazorpayOrderNoteSchema(BaseModel):
    user_id: str
    company_id: str
    subscription_id: str

    class Config:
        extra = 'allow'


class RazorpayOrderSchema(BaseModel):
    id: str
    entity: str
    amount: PositiveInt
    amount_paid: int = 0
    amount_due: int = 0
    currency: str = Currency.INR
    receipt: Union[str, None]
    offer_id: Union[str, None]
    status: str
    attempts: Union[int, None]
    notes: RazorpayOrderNoteSchema
    created_at: Union[int, None]
    short_url: str = ''

    class Config:
        extra = 'allow'


class RazorpayPaymentLinkCustomerSchema(BaseModel):
    contact: str
    email: str
    name: str


class RazorpayPaymentLinkNoteSchema(BaseModel):
    current_plan: str
    current_plan_id: str
    current_subscription_id: str
    billing_cycle: str = BillingCycle.ONE_TIME
    for_entity: str = Field(default='order')
    validity: str = 'lifetime'
    prorated: bool = False


class RazorpayPaymentLinkNotifySchema(BaseModel):
    email: bool
    sms: bool
    whatsapp: bool


class RazorpayPaymentLinkPaymentSchema(BaseModel):
    amount: PositiveInt
    created_at: Union[int, None]
    method: str
    payment_id: str
    status: str


class RazorpayPaymentLinkReminderSchema(BaseModel):
    status: Union[str, None]


class RazorpayPaymentLinkSchema(BaseModel):
    accept_partial: bool
    amount: PositiveInt
    amount_paid: int = 0
    callback_method: str
    callback_url: str
    cancelled_at: Union[int, None]
    created_at: Union[int, None]
    currency: str
    customer: RazorpayPaymentLinkCustomerSchema = Field(default_factory=dict)
    customer_id: str
    description: str
    expire_by: Union[int, None]
    expired_at: Union[int, None]
    first_min_partial_amount: int
    id: str
    notes: RazorpayPaymentLinkNoteSchema = Field(default_factory=dict)
    notify: RazorpayPaymentLinkNotifySchema = Field(default_factory=dict)
    order_id: str = ''
    payments: Union[list[RazorpayPaymentLinkPaymentSchema], None] = Field(default_factory=list)
    reference_id: Union[str, None]
    reminder_enable: bool
    reminders: Union[RazorpayPaymentLinkReminderSchema, list] = Field(default_factory=dict)
    short_url: str
    status: str
    updated_at: Union[int, None]
    upi_link: bool
    user_id: str
    whatsapp_link: bool


class RazorpayPaymentCardSchema(BaseModel):
    id: str
    entity: str
    name: str = ''
    last4: str = ''
    network: str = ''
    type: str = ''
    issuer: str = ''
    international: bool
    emi: bool
    sub_type: str
    token_iin: Union[str, int, None]


class RazorpayPaymentAcquirerSchema(BaseModel):
    auth_code: Union[str, int, None]


class RazorpayPaymentSchema(BaseModel):
    id: str
    entity: str
    amount: PositiveInt
    currency: str
    status: str
    order_id: str
    invoice_id: str
    international: bool
    method: str
    amount_refunded: int
    refund_status: Union[str, None]
    captured: bool
    description: str
    card_id: str
    card: RazorpayPaymentCardSchema
    bank: Union[str, None]
    wallet: Union[str, None]
    vpa: Union[str, None]
    email: EmailStr
    contact: str
    customer_id: str
    token_id: str
    notes: Union[list, dict]
    fee: int
    tax: int
    error_code: Union[str, int, None]
    error_description: Union[str, None]
    error_source: Union[str, None]
    error_step: Union[str, int, None]
    error_reason: Union[str, None]
    acquirer_data: RazorpayPaymentAcquirerSchema
    created_at: Union[int, None]


class RazorpayInvoiceCustomerSchema(BaseModel):
    id: str | None = ''
    name: Union[str, None] = None
    email: Optional[EmailStr] = None
    contact: str | None = ''
    gstin: Union[str, None] = None
    billing_address: Union[dict, None] = None
    shipping_address: Union[dict, None] = None
    customer_name: Union[str, None] = None
    customer_email: Optional[EmailStr] = None
    customer_contact: str | None = ''


class RazorpayInvoiceLineItemSchema(BaseModel):
    id: str | None = ''
    item_id: Union[str, None] = None
    ref_id: Union[str, None] = None
    ref_type: Union[int, str, None] = None
    name: str | None = ''
    description: Union[str, None] = None
    amount: Optional[PositiveInt] = 0
    unit_amount: Optional[PositiveInt] = 0
    gross_amount: Optional[PositiveInt] = 0
    tax_amount: int = 0
    taxable_amount: Union[PositiveInt, None] = None
    net_amount: Union[PositiveInt, None] = None
    currency: str | None = ''
    type: str | None = ''
    tax_inclusive: Optional[bool] = None
    hsn_code: Union[str, None] = None
    sac_code: Union[str, None] = None
    tax_rate: Union[str, None] = None
    unit: Union[int, str, None] = None
    quantity: int | None = 0
    taxes: list = []


class RazorpayInvoiceNoteSchema(BaseModel):
    user_id: str | None = ''
    company_id: str | None = ''
    subscription_id: str | None = ''


class RazorpayInvoiceSchema(BaseModel):
    id: str | None = Field(default_factory=get_uuid)
    entity: str | None = 'invoice'
    receipt: Union[str, None] = None
    invoice_number: Union[str, None] = None
    customer_id: str | None = ''
    customer_details: RazorpayInvoiceCustomerSchema = Field(
        default_factory=RazorpayInvoiceCustomerSchema)
    order_id: str | None = ''
    subscription_id: str | None = ''
    line_items: list[RazorpayInvoiceLineItemSchema] = []
    payment_id: str | None = ''
    notes: RazorpayInvoiceNoteSchema = Field(
        default_factory=RazorpayInvoiceNoteSchema)
    status: str | None = ''
    expire_by: Union[int, None] = None
    issued_at: Union[int, None] = None
    paid_at: Union[int, None] = None
    cancelled_at: Union[int, None] = None
    expired_at: Union[int, None] = None
    sms_status: Union[int, None] = None
    email_status: Union[int, None] = None
    date: Union[int, None] = None
    terms: Union[str, None] = None
    partial_payment: Optional[bool] = None
    gross_amount: Optional[PositiveInt] = 0
    tax_amount: int = 0
    taxable_amount: Optional[PositiveInt] = 0
    amount: Optional[PositiveInt] = 0
    amount_paid: Optional[PositiveInt] = 0
    amount_due: int = 0
    currency: str | None = Currency.INR
    currency_symbol: str | None = '₹'
    description: Union[str, None] = None
    comment: Union[str, None] = None
    short_url: str | None = ''
    view_less: Optional[bool] = None
    billing_start: Union[int, None] = None
    billing_end: Union[int, None] = None
    type: str | None = 'invoice'
    group_taxes_discounts: Optional[bool] = None
    created_at: Union[int, None] = None
    idempotency_key: Union[int, str, None] = None
    ref_num: Union[int, str, None] = None

    class Config:
        extra = 'allow'