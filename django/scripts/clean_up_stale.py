import logging
from datetime import timedelta
from utils import DateUtil
from utils.mongo import MongoUtility
from utils.constants import (
    OrderStatus,
    DBColls
)

logger = logging.getLogger('application')
# python manage.py runscript pre_debit_notification


def run():
    logger.info('Running Clean Up Stale Records Job!!')

    db = MongoUtility(get_new=True)
    date_filter = DateUtil.get_current_timestamp()
    date_filter = DateUtil.convert_to_datetime(date_filter)
    date_filter = date_filter - timedelta(days=30)
    date_filter = int(date_filter / 1000)

    query = {
        'created_at': {
            '$lte': date_filter
        },
        'status': OrderStatus.CREATED
    }
    db.delete(DBColls.RAZORPAY_ORDERS, query, delete_many=True)

    message = "Pre Debit Notification Job Completed"
    logger.info(message)
    db.client.close()
    return message
