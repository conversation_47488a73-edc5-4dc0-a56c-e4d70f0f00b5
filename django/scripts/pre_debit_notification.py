import logging
from utils import (
    send_email,
    send_sms,
    DateUtil
)
from utils.mongo import MongoUtility
from utils.constants import (
    SubscriptionStatus,
    SMSTemplateId,
    PRECISSION,
    DBColls
)

logger = logging.getLogger('application')
# python manage.py runscript pre_debit_notification

def run():
    logger.info('Running Pre Debit Notification Job!!')

    db = MongoUtility(get_new=True)
    now = DateUtil.get_current_timestamp()
    today = DateUtil.convert_to_datetime(now).date()

    subscriptions = []
    plan_ids, company_ids, rzp_sub_ids = [], [], []
    query = {
        'status_id': {
            '$in': [
                SubscriptionStatus.TRIAL.value,
                SubscriptionStatus.ACTIVE.value,
            ]
        }
    }
    subscription_objs = db.find(DBColls.SUBSCRIPTIONS, query)
    for subscription in subscription_objs:
        plan_ids.append(subscription['plan_id'])
        company_ids.append(subscription['company_id'])

        if subscription.get('razorpay_sub_id'):
            rzp_sub_ids.append(subscription['razorpay_sub_id'])

        subscriptions.append(subscription)

    plan_query = {'id': {'$in': plan_ids}}
    df = {'_id': 0, 'id': 1, 'razorpay_plan_id': 1}
    plans = db.find(DBColls.PLANS, plan_query, data_filter=df)

    plan_vs_rzp_plan = {p['id']: p['razorpay_plan_id'] for p in plans}

    rzp_query = {'id': {'$in': list(plan_vs_rzp_plan.values())}}
    rzp_plans = {p['id']: p for p in db.find(DBColls.RAZORPAY_PLANS, rzp_query)}

    company_query = {'id': {'$in': company_ids}}
    company_id_vs_company = {c['id']: c for c in db.find(DBColls.COMPANIES, company_query)}

    rzp_sub_id_vs_user_id = {}
    rzp_sub_query = {'id': {'$in': rzp_sub_ids}}
    for r in db.find(DBColls.RAZORPAY_SUBSCRIPTIONS, rzp_sub_query):
        notes = r.get('notes') or {}
        user_id = notes.get('user_id')
        if not user_id:
            continue

        rzp_sub_id_vs_user_id['id'] = user_id

    user_query = {'id': {'$in': list(rzp_sub_id_vs_user_id.values())}}
    users = {u['id']: u for u in db.find(DBColls.USERS, user_query)}

    for subscription in subscriptions:
        sub_id = subscription['id']
        if subscription['status_id'] == SubscriptionStatus.TRIAL.value:
            due_date = DateUtil.convert_to_datetime(subscription['end_date']).date()
        else:
            due_date = DateUtil.convert_to_datetime(subscription['current_end']).date()

        if not due_date:
            logger.error(f'Due date (end_date/current_end) not available in subscription({sub_id}), Skipping...')
            continue

        rzp_plan_id = plan_vs_rzp_plan.get(subscription['plan_id'])
        if not rzp_plan_id:
            logger.error(f'Razorpay plan ID (razorpay_plan_id) not available in subscription({sub_id}), Skipping...')
            continue

        rzp_plan = rzp_plans.get(rzp_plan_id) or {}
        price = rzp_plan.get('item', {}).get('amount')
        if not price:
            logger.error(f'Plan amount not available in subscription({sub_id}), Skipping...')
            continue

        price = round((price / 100), PRECISSION)  # in rupees
        price = int(price) if (price == int(price)) else price

        receivers = []
        sms_receivers = []
        company = company_id_vs_company.get(subscription['company_id']) or {}
        if not company:
            logger.error(logger.error(f'Company details not available in subscription({sub_id}), Skipping...'))
            continue

        company_contact = company['phone']
        receivers.append(company['email'])

        company_name = company['name']
        if not company_name:
            logger.error(f'Company name not available in subscription({sub_id}), Skipping...')
            continue

        if company_contact:
            sms_receivers.append(company_contact)

        ccs, user_contact = [], None
        user_id = rzp_sub_id_vs_user_id.get(subscription['razorpay_sub_id'])
        if user_id:
            user = users.get(user_id)
            if not user:
                logger.error(f'User details not available in subscription({sub_id}), [Optional]')
            else:
                ccs.append(user['email'])
                user_contact = user['phone']

        if user_contact:
            sms_receivers.append(user_contact)

        due_date_str = DateUtil.custom_strftime(due_date)
        email_template = 'pre_debit_notification.html'
        email_subject = f"Upcoming auto-debit at SCLEN.AI; Due on {due_date_str}"
        email_message = {
            'due_date_str': due_date_str,
            'company_name': company_name,
            'price': price,
        }

        days_left = (due_date - today).days
        if days_left in [7, 1]: # 7 Days or 1 day before
            send_email(
                receivers,
                ccs,
                email_subject,
                email_message,
                attachments=None,
                template=email_template,
                sender=None
            )

            if not sms_receivers:
                logger.error(f'SMS Contact details not available for subscription({sub_id}), Skipping...')
                continue

            message_params = [price]
            sms_message = (
                f"Your SCLEN.AI fee of Rs.{price} is due on {due_date_str}. Auto-debit is active. "
                "Pls ensure sufficient balance. Bank may levy charges if it fails. Ignore if paid."
            )
            params = {
                'receivers': sms_receivers,
                'message': sms_message,
                'template_id': SMSTemplateId.MCP_SMS_TXN,
                'user_details': {
                    'company_id': company['id'],
                    'company_name': company_name
                },
                'message_params': message_params,
                'alert_type': 'Pre Debit Notification',
                'module_id': 2
            }
            send_sms(params)

    message = "Pre Debit Notification Job Completed"
    logger.info(message)
    db.client.close()
    return message
