import logging
from utils.mongo import MongoUtility
from utils.constants import (
    SubscriptionStatus,
    DBColls
)
from utils.date_util import DateUtil

logger = logging.getLogger("application")


def run():
    """
    Handle trial subscriptions where autopay was revoked:
    1. Find trial subscriptions with end_date in the past
    2. Check if their Razorpay subscription was cancelled
    3. Update their status to CANCELLED in our system
    """
    logger.info("Running Subscription Auto Updates Job!!")
    logger.info("Checking for expired trial subscriptions with revoked autopay...")

    db = MongoUtility(get_new=True)
    now = DateUtil.get_current_timestamp()

    query = {
        "status_id": SubscriptionStatus.TRIAL.value,
        "end_date": {"$lt": now}
    }
    cancelled = SubscriptionStatus.CANCELLED
    expired_trials = db.find(DBColls.SUBSCRIPTIONS, query)
    rzp_sub_ids = [ct["razorpay_sub_id"] for ct in expired_trials if ct.get("razorpay_sub_id")]

    query = {
        "id": {"$in": rzp_sub_ids},
        "status": cancelled.name.lower()
    }
    rzp_sub_ids = [r["id"] for r in db.find(DBColls.RAZORPAY_SUBSCRIPTIONS, query)]

    query = {"razorpay_sub_id": {"$in": rzp_sub_ids}}
    update_query = {
        "status": cancelled.name,
        "status_id": cancelled.value,
        "remarks": (
            "Auto pay was either revoked or payment retries "
            "failed. Please contact support for more details."
        )
    }
    db.update(DBColls.SUBSCRIPTIONS, query, update_query, update_many=True)

    logger.info("Updated subscriptions to CANCELLED status")
    message = "Subscription Auto Updates Job Completed"
    logger.info(message)
    db.client.close()
    return message
