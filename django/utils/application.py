import json
import redis
import logging
from django.conf import settings
from redis.exceptions import ConnectionError

logger = logging.getLogger('application')


class RedisUtils(object):

    def __init__(self):
        self.redis_conn = redis.from_url(settings.REDIS_SESSION_DB_URL)

    def get_data(self, key):
        try:
            data = self.redis_conn.get(key) or {}
        except ConnectionError:
            logger.error('Unable to connect to redis to get_data.')
            data = {}

        if data:
            string_data = data.decode('utf-8')
            json_string = string_data.replace("'", '"')
            data = json.loads(json_string)
        return data

    def set_data(self, key, data, expiry=None):
        expiry = expiry or 0
        if not isinstance(expiry, int):
            expiry = 0

        new_data = self.get_data(key) or data
        new_data = json.dumps(new_data)
        new_data = new_data.encode('utf-8')

        try:
            if expiry:
                self.redis_conn.set(key, new_data, ex=expiry)
            else:
                self.redis_conn.set(key, new_data)
        except ConnectionError:
            logger.error('Unable to connect to redis to set_data.')
