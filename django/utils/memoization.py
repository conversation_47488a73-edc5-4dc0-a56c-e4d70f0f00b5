from utils.mongo import MongoUtility
from utils.constants import DBColls


class Memoization:
    sms_vendor_configs = {}
    states_list = []
    app_domains = {}
    app_paths = {}
    saas_settings = {}

    @staticmethod
    def get_sms_vendor_config(vendor_type):
        if vendor_type in Memoization.sms_vendor_configs:
            return Memoization.sms_vendor_configs[vendor_type]

        db = MongoUtility()
        config = db.find(DBColls.SMS_VENDORS, {'id': vendor_type}, find_one=True)
        Memoization.sms_vendor_configs[vendor_type] = config
        return config

    @staticmethod
    def get_app_url_data(domain_id, path_id):
        try:
            domain = Memoization.app_domains[domain_id]
            path = Memoization.app_paths[path_id]
        except KeyError:
            db = MongoUtility()
            obj = db.find(DBColls.APP_URLS, {'id': domain_id}, find_one=True)

            if domain_id not in Memoization.app_domains:
                Memoization.app_domains[domain_id] = obj['fe_domain'].strip('/')

            for key, value in obj['fe_paths'].items():
                if key not in Memoization.app_paths:
                    Memoization.app_paths[key] = value

            domain = Memoization.app_domains[domain_id]
            path = Memoization.app_paths[path_id]

        return {
            "domain": domain,
            "path": path,
            "url": f"{domain}{path}"
        }

    @staticmethod
    def get_saas_settings():
        if not Memoization.saas_settings:
            db = MongoUtility()
            Memoization.saas_settings = db.find(DBColls.SAAS_SETTINGS, {}, find_one=True)
        return Memoization.saas_settings
