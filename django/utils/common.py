import time
import json
import logging
import requests
import traceback
import num2words
from uuid import uuid4
from urllib import parse
from .mongo import MongoUtility
from .constants import (
    SEQUENCE_TYPE_PREFIX,
    DBColls
)
from .date_util import DateUtil


logger = logging.getLogger('application')


def get_uuid():
    return str(uuid4()).replace('-', '')


def number_to_words(number):
    return num2words.num2words(number, lang='en_IN').title()


def get_parsed_url_data(url):
    data = {}
    parsed_obj = parse.urlsplit(url)
    scheme, host = parsed_obj.scheme, parsed_obj.netloc
    if scheme and host:
        data.update({
            'origin': '{}://{}'.format(scheme, host),
            'scheme': scheme,
            'host': host,
            'path': parsed_obj.path,
        })
    return data


def get_client_details(request):
    meta_data = request.META
    meta_keys = [
        'HTTP_X_FORWARDED_FOR',
        'REMOTE_ADDR',
        'HTTP_HOST',
        'REQUEST_METHOD',
        'PATH_INFO',
        'SERVER_PROTOCOL',
        'CONTENT_TYPE',
        'HTTP_USER_AGENT',
    ]

    details = {key.lower(): meta_data.get(key) for key in meta_keys}

    x_forwarded_for = details['http_x_forwarded_for']
    if x_forwarded_for:
        details['http_x_forwarded_for'] = x_forwarded_for.split(',')[0]
    return details


def get_traceback(e):
    try:
        return ''.join(traceback.TracebackException.from_exception(e).format())
    except Exception as err:
        return 'Error occurred while fetching traceback of original error ({}). {}'.format(e, err)


def fetch_response(request_url, params=None, headers=None, method='GET', payload='', timeout=30):
    if headers and ('cache-control' not in headers):
        headers.update({'cache-control': 'no-cache'})
    return requests.request(method, request_url, data=payload, headers=headers, params=params, timeout=timeout)


def execute_task_in_thread(target, log_msg, args=(), **kwargs):
    from threading import Thread
    logger.info(log_msg)

    delay = int(kwargs.get('delay') or 0)
    if delay:
        time.sleep(delay)

    thread_inst = Thread(target=target, args=args)
    thread_inst.start()
    # logger.info(f'After sap trigger.. {thread_inst.is_alive()}')
    return thread_inst


def log_api_request(api_type, request, company_id=None, logs_coll=DBColls.API_REQUEST_LOGS, **kwargs):
    try:
        db = MongoUtility()

        try:
            request_method = request.method
        except AttributeError:
            request_method = 'GET'

        request_body = None
        if request_method.lower() == 'post':
            try:
                request_body = kwargs.get('request_payload', {})
                if isinstance(request_body, str):
                    try:
                        request_body = json.loads(request_body)
                    except ValueError:
                        pass
            except (AttributeError, ValueError):
                request_body = {}

        try:
            url = request.get_raw_uri()
        except AttributeError:
            url = None  # noqa

        obj = {
            'api_type': api_type.value,
            'company_id': company_id,
            'api_name': api_type.name,
            'request_url': url,
            'request_method': request_method,
            'request_body': request_body,
            # 'request_headers': headers,
            # 'response_status_code': 200,
            # 'response_body': raw_response,
            # 'response_type': response_type,
            'created_on': DateUtil.get_current_timestamp(),
            'datetime': DateUtil.get_current_timestamp(True),
            **kwargs
        }
        db.insert(logs_coll, [obj])
    except Exception as e:
        logger.error('[Unhandled Exception] {}'.format(get_traceback(e)))
        return False
    return True


class DummyClass(object):

    def __init__(self, dict_obj={}):
        for key, value in dict_obj.items():
            setattr(self, key, value)


class DummyUtility(object):
    def __init__(self, request):
        self.request = request


def process_payload_errors(errors_list):
    errors = []
    for error in errors_list:
        msg = error['msg']
        loc = error.get('loc')
        if loc:
            loc = loc[0].upper()
            errors.append(f"[{loc.replace('_', ' ')}] {msg}")
        else:
            errors.append(f"{msg}")
    errors = ', '.join(errors)
    return errors


def generate_sequence_no(sequence_type, sequence_start=1001):
    """
        Depending on sequence_type value sequence numbers will be generated
        fot Invoice or Receipt

        Args:
            sequence_type [int]: Sequence Type (1 = Invoice, 2 = Receipt)
            sequence_start [int]: 10001

        Returns [str]:
            Invoice number will look like this: INV-1745237256-1001
                INV: Prefix
                1745237256: Current time in Unix (secs)
                1001: Sequence start

            Receipt number will look like this: REC-1745237256-1001
                REC: Prefix
                1745237256: Current time in Unix (secs)
                1001: Sequence start
    """
    db = MongoUtility()
    prefix = SEQUENCE_TYPE_PREFIX[sequence_type]
    now_in_secs = DateUtil.get_current_timestamp() // 1000

    query = {
        'type': sequence_type,
        'seq_no': {'$exists': True},
        'prefix': prefix
    }

    result = db.update(
        DBColls.SEQUENCE_NUMBER,
        query,
        inc_query={'seq_no': 1},
        find_one_and_update=True
    )

    if result is None:
        query = {
            'type': sequence_type,
            'prefix': prefix
        }
        set_query = {'seq_no': sequence_start}
        result = db.update(
            DBColls.SEQUENCE_NUMBER,
            query,
            set_query=set_query,
            upsert=True,
            find_one_and_update=True
        )

    sequence_number = f'{prefix}{now_in_secs}{result["seq_no"]}'
    return sequence_number