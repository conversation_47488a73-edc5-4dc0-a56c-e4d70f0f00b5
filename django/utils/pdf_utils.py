

def convert_html_template_to_pdf(data: dict, template: str, css_urls: list):
    from django.template.loader import render_to_string
    from weasyprint import HTML

    # Render the Django template to an HTML string
    html_content = render_to_string(template, data)

    # Generate PDF directly from the HTML string
    pdf = HTML(string=html_content).write_pdf(stylesheets=css_urls)
    # Save or return the PDF
    # with open('bill.pdf', 'wb') as f:
    #     f.write(pdf)
    return pdf
