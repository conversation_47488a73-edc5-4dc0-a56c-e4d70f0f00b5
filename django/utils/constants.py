from zoneinfo import ZoneInfo
from enum import Enum


IST_TZ = ZoneInfo('Asia/Kolkata')
UTC_TZ = ZoneInfo('UTC')
PRECISSION = 2


class SuccessMessages:
    SUCCESS = 'Success'
    OKAY = 'Looks like everything went okay'
    UPDATE_SUCCESS = 'Data updated successfully'
    RETRIEVE_SUCCESS = 'Data retrieved successfully'
    SUBSCRIPTION_ACTIVATED = (
        'Subscription Activated! Your payment has been successfully processed '
        '& verified. Enjoy uninterrupted access to your subscription.'
    )
    GENERATING_INVOICE = 'Processing payments, generating invoice, please wait.'
    CAPTURING_PAYMENTS = 'Processing payments, invoice generated, capturing payments, please wait'
    ALREADY_SUBSCRIBED = 'You are already subscribed to this plan. No further action is needed.'
    PROCEED_TO_PAY = 'Please proceed to payments to activate the service.'
    PAYMENT_SUCCESS = 'Your payment has been successfully processed & verified.'


class ErrorMessages:
    INVALID_TOKEN = 'Invalid token'
    ACCESS_DENIED = 'Access Denied'
    TECHNICAL_ERROR = 'OOPS!! Something went wrong. Please try again after sometime.'
    SMS_ALERT = '[SMS_ALERT_ERROR]'
    EMAIL_ALERT = '[EMAIL_ALERT_ERROR]'
    VERIFYING_PAYMENT = (
        "We're verifying your payment. If the amount was deducted, it "
        "will be confirmed shortly. If not, please contact support."
    )
    SUBSCRIPTION_NOT_FOUND = 'Subscription does not exist. Please contact support.'
    INVOICE_NOT_FOUND = 'Invoice does not exist. Please contact support.'
    ORDER_DOES_NOT_EXIST = 'Order does not exist. Please contact support.'


class EmailHeader:
    SUBSCRIPTION_ACTIVATED = 'Subscription Activated at SCLEN.AI'
    SUBSCRIPTION_UPGRADED = 'Subscription Updated at SCLEN.AI'
    ORDER_CREATED = 'Order Created at SCLEN.AI'

class SMSTemplateId:
    MCP_SMS_TXN = 'MCP_SMS_TXN'


class Status:
    FORBIDDEN = 403
    UNAUTHORIZED = 401
    BAD_REQUEST = 400
    SERVER_ERROR = 500
    NOT_FOUND = 404
    OK = 200


class DBColls:
    APP_URLS = 'app_urls'
    COMPANY_TYPE = 'company_type'
    USER_TYPE = 'user_type'
    USER_ROLES = 'user_roles'
    PERMISSIONS = 'permissions'
    USER_PERMISSIONS = 'user_permissions'
    CELERY_SCHEDULES = 'celery_schedules'
    MODULES = 'modules'
    UI_CONFIG = 'ui_config'
    COMPANIES = 'companies'
    USERS = 'users'
    USER_SESSIONS = 'user_sessions'
    LOGIN_LOGS = 'login_logs'
    USER_ACTIVITY_LOGS = 'user_activity_logs'
    ACCESS_TOKEN = 'access_token'
    OTP_DETAILS = 'otp_details'
    BLOCKED_USERS = 'blocked_users'
    SMS_VENDORS = 'sms_vendors'
    SMS_SENT_LOGS = 'sms_sent_logs'
    API_REQUEST_LOGS = 'api_request_logs'
    STATES_LIST = 'states_list'
    SEQUENCE_NUMBER = 'sequence_number'

    # SCLEN Plans & Subscriptions
    PLANS = 'plans'
    SUBSCRIPTIONS = 'subscriptions'

    # Razorpay collections
    RAZORPAY_CUSTOMERS = 'razorpay_customers'
    RAZORPAY_INVOICES = 'razorpay_invoices'
    RAZORPAY_PLANS = 'razorpay_plans'
    RAZORPAY_SUBSCRIPTIONS = 'razorpay_subscriptions'
    RAZORPAY_ORDERS = 'razorpay_orders'
    RAZORPAY_PAYMENTS = 'razorpay_payments'

    WEBHOOK_LOGS = 'webhook_logs'
    SAAS_SETTINGS = 'saas_settings'


class CompanyType:
    SEEKER = 1
    PROVIDER = 2


class UserType:
    SEEKER = 1
    PROVIDER = 2
    DRIVER = 3


class UserRole(Enum):
    SUPER_ADMIN = 1
    ADMIN_SEEKER = 10
    SEEKER = 20
    ADMIN_PROVIDER = 40
    PROVIDER = 30


class AppDomainID:
    ADMIN = 'admin'


class AppPathID:
    ADMIN_PROFILE = 'admin_profile'


class SMSVendor:
    BOSCHINDIA = 2


class Currency:
    INR = 'INR'
    USD = 'USD'


class RazorpayInvoiceStatus(object):
    DRAFT = 'draft'
    ISSUED = 'issued'
    PARTIALLY_PAID = 'partially_paid'
    PAID = 'paid'
    CANCELLED = 'cancelled'
    EXPIRED = 'expired'
    DELETED = 'deleted'


class RazorpayPaymentStatus(object):
    CAPTURED = 'captured'
    FAILED = 'failed'
    REFUNDED = 'refunded'


class SequenceType:
    INVOICE_NUMBER = 'invoice_no'
    RECEIPT_NUMBER = 'receipt_no'


SEQUENCE_TYPE_PREFIX = {
    SequenceType.INVOICE_NUMBER: 'INV',
    SequenceType.RECEIPT_NUMBER: 'REC'
}


class SubscriptionStatus(Enum):
    PENDING = 1
    ACTIVE = 2
    PAUSED = 3
    EXPIRED = 4
    CANCELLED = 5
    HALTED = 6
    COMPLETED = 7
    TRIAL = 8
    IN_REVIEW = 9
    REJECTED = 10


class SAASProduct(Enum):
    NETWORK = 1
    PROCUREMENT = 2
    OPTIMIZATION = 3
    EXECUTION = 4
    VISIBILITY = 5
    RECONCILIATION = 6
    ANALYTICS = 7
    ORCHESTRATION = 8


class SAASModules(Enum):
    SUPER_ADMIN = 1
    ADMIN = 2
    PROCUREMENT = 3
    VENDOR_ONBOARDING = 4
    VENDOR_EXPLORER = 5
    FREIGHT_INDEX = 6
    CARBON_EMISSIONS = 7


class BillingCycle(object):
    ONE_TIME = 'one_time'
    DAILY = 'daily'
    WEEKLY = 'weekly'
    MONTHLY = 'monthly'
    QUARTERLY = 'quarterly'
    ANNUALLY = 'annually'


BILLING_CYCLES = {
    BillingCycle.DAILY: 7,
    BillingCycle.WEEKLY: 7,
    BillingCycle.MONTHLY: 12,
    BillingCycle.QUARTERLY: 4,
    BillingCycle.ANNUALLY: 1
}


class DiscountType(object):
    PERCENTAGE = 1
    FLAT = 2


class WebhookSource(Enum):
    RAZORPAY = 1
    MANUAL = 2

class SubscriptionEvent:
    SUBSCRIBE = 'subscribe'
    UPGRADE = 'upgrade'
    PAYG = 'payg'  # Pay-as-you-go


class OrderStatus:
    CREATED = 'created'
    PAID = 'paid'


PRODUCT_TRAIL_KEYS = {
    SAASProduct.NETWORK.value: "network_trial_used",
    SAASProduct.PROCUREMENT.value: "procurement_trial_used",
    SAASProduct.OPTIMIZATION.value: "optimization_trial_used",
    SAASProduct.EXECUTION.value: "execution_trial_used",
    SAASProduct.VISIBILITY.value: "visibility_trial_used",
    SAASProduct.RECONCILIATION.value: "recon_trial_used",
    SAASProduct.ANALYTICS.value: "analytics_trial_used",
    SAASProduct.ORCHESTRATION.value: "orchestration_trial_used",
}

class TrialAmount(Enum):
    FREE = 0
    ONE = 1
    TWO = 2
    THREE = 3
    FOUR = 4
    FIVE = 5


class UsageKeys(Enum):
    NUMBER_OF_LANES = '0c8d39e4fff90959655abfd1fcb3355b'
