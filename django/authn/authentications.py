from rest_framework import authentication, exceptions, status
from utils import ErrorResponse, RedisUtils
from utils.constants import (
    ErrorMessages,
    CompanyType,
    DBColls,
    UserType,
    UserRole
)
from utils.mongo import MongoUtility


def get_session_data(request, raise_error=False):
    request.is_token_valid = False
    request.is_seeker = False
    request.is_provider = False
    request.is_driver = False

    headers = request.headers
    user_agent = headers.get('user-agent', 'Unkown')
    token = headers.get('token', None)

    redis_inst = RedisUtils()
    session = {}
    if token:
        session = redis_inst.get_data(token)
        if not session:
            db = MongoUtility()
            session = db.find(DBColls.USER_SESSIONS, {'token': token}, {'_id': 0, 'datetime': 0}, find_one=True)
            redis_inst.set_data(token, session)

    if not session:
        if raise_error:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.AUTHENTICATION_ERROR,
                    ErrorMessages.INVALID_TOKEN,
                    status.HTTP_401_UNAUTHORIZED
                )
            )
        return request

    request.user_agent = user_agent
    request.token = token
    request.force_auth = session.get('force_auth', False)
    request.is_sso_login = session.get('is_sso_login', False)
    request.company_id = session.get('company_id')
    request.company_type = session.get('company_type')
    request.company_name = session.get('company_name')
    request.user_id = session.get('user_id')
    request.user_type = session.get('user_type')
    request.user_role = session.get('user_role')
    request.user_name = session.get('user_name', '')
    request.email = session.get('email')
    request.user_permissions = session.get('permissions', [])
    request.is_seeker = request.company_type == CompanyType.SEEKER
    request.is_provider = request.company_type == CompanyType.PROVIDER
    request.is_driver = request.user_type == UserType.DRIVER
    request.is_token_valid = True
    return request


class AuthenticateSeekerAdmin(authentication.BaseAuthentication):
    """This authentication is used to only authorize seeker admins."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not request.is_seeker and request.user_role == UserRole.ADMIN.value:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED,
                    status.HTTP_403_FORBIDDEN
                )
            )
        return ({}, None)


class AuthenticateSeeker(authentication.BaseAuthentication):
    """This authentication is used to only authorize seekers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not request.is_seller:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED
                )
            )
        return ({}, None)


class AuthenticateProvider(authentication.BaseAuthentication):
    """This authentication is used to only authorize providers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not request.is_provider:
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED
                )
            )
        return ({}, None)


class AuthenticateAll(authentication.BaseAuthentication):
    """This authentication is used to only authorize seekers or providers. This does not authorize drivers."""

    def authenticate(self, request):
        request = get_session_data(request, raise_error=True)
        if not (request.is_seller or request.is_buyer or request.is_provider):
            raise exceptions.AuthenticationFailed(
                ErrorResponse.get_response_obj(
                    ErrorResponse.ACCESS_DENIED,
                    ErrorMessages.ACCESS_DENIED
                )
            )
        return ({}, None)
